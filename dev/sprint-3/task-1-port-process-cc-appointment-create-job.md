# Task 1: Port ProcessCcAppointmentCreate Job

## Description
Implement appointment creation job with CC data transformation and AP sync. This ports the legacy `ProcessCcAppointmentCreate` job to handle appointment creation events from the CC platform.

## Acceptance Criteria
- [ ] Appointment creation job processor implemented using BaseJobProcessor
- [ ] CC appointment data transformation to AP format
- [ ] Patient linking and contact sync integration
- [ ] Service mapping and resource allocation
- [ ] Never use `any` or `as any` - use proper TypeScript types
- [ ] Comprehensive error handling and logging

## Technical Implementation Details

### 1. Create Appointment Job Types
Create `src/types/appointmentJobs.ts`:

```typescript
import type { GetCCAppointmentType } from '@libCC/CCTypes'
import type { PostAPAppointmentType } from '@libAP/APTypes'

export interface ProcessCcAppointmentCreatePayload {
  id: number
  title?: string
  startsAt: string
  endsAt: string
  patients: number[]
  services?: number[]
  canceledAt?: string
  canceledWhy?: string
  firstOfPatient?: boolean
  updatedAt: string
  createdAt: string
}

export interface AppointmentSyncContext {
  ccAppointmentData: GetCCAppointmentType
  location: string
  requestId: string
  primaryPatient?: {
    ccId: number
    apId: string
  }
  linkedPatients: Array<{
    ccId: number
    apId: string
  }>
}

export interface AppointmentSyncResult {
  success: boolean
  action: 'created' | 'updated' | 'skipped' | 'error'
  appointment?: {
    id: string
    apId: string
    ccId: number
  }
  apAppointment?: any // AP appointment data
  patientsLinked: number
  servicesLinked: number
  message: string
}
```

### 2. Create Appointment Service
Create `src/services/appointmentService.ts`:

```typescript
import { getDb } from '@database'
import { appointment, patient } from '@database/schema'
import { eq, inArray } from 'drizzle-orm'
import { logger, ErrorLogger } from '@utils'
import type { GetCCAppointmentType } from '@libCC/CCTypes'

export class AppointmentService {
  private db = getDb()

  /**
   * Search for existing appointment by CC ID
   */
  async findAppointmentByCcId(
    ccId: number,
    requestId?: string
  ): Promise<{ found: boolean; appointment?: any }> {
    try {
      const existing = await this.db
        .select()
        .from(appointment)
        .where(eq(appointment.ccId, ccId))
        .limit(1)

      if (existing.length > 0) {
        logger.info('Appointment found by CC ID', {
          requestId,
          ccId,
          appointmentId: existing[0].id,
          apId: existing[0].apId
        })

        return {
          found: true,
          appointment: existing[0]
        }
      }

      return { found: false }

    } catch (error) {
      await ErrorLogger.logError(
        'AppointmentSearch',
        error,
        { ccId, requestId },
        'AppointmentService'
      )

      return { found: false }
    }
  }

  /**
   * Get linked patients for appointment
   */
  async getLinkedPatients(
    ccPatientIds: number[],
    requestId?: string
  ): Promise<Array<{ ccId: number; apId: string; id: string }>> {
    try {
      if (ccPatientIds.length === 0) {
        return []
      }

      const linkedPatients = await this.db
        .select({
          id: patient.id,
          ccId: patient.ccId,
          apId: patient.apId
        })
        .from(patient)
        .where(inArray(patient.ccId, ccPatientIds))

      const validPatients = linkedPatients.filter(p => p.apId && p.ccId)

      logger.info('Linked patients found', {
        requestId,
        ccPatientIds,
        foundCount: validPatients.length,
        totalRequested: ccPatientIds.length
      })

      return validPatients as Array<{ ccId: number; apId: string; id: string }>

    } catch (error) {
      await ErrorLogger.logError(
        'GetLinkedPatients',
        error,
        { ccPatientIds, requestId },
        'AppointmentService'
      )

      return []
    }
  }

  /**
   * Create or update appointment record in database
   */
  async createOrUpdateAppointment(
    ccAppointmentData: GetCCAppointmentType,
    apId?: string,
    requestId?: string
  ): Promise<{ id: string; apId: string; ccId: number }> {
    try {
      const existing = await this.findAppointmentByCcId(ccAppointmentData.id, requestId)

      if (existing.found && existing.appointment) {
        // Update existing appointment
        const updated = await this.db
          .update(appointment)
          .set({
            apId: apId || existing.appointment.apId,
            ccUpdatedAt: new Date(ccAppointmentData.updatedAt),
            ccData: ccAppointmentData,
            updatedAt: new Date()
          })
          .where(eq(appointment.id, existing.appointment.id))
          .returning()

        logger.info('Appointment record updated', {
          requestId,
          appointmentId: updated[0].id,
          ccId: ccAppointmentData.id,
          apId: apId || existing.appointment.apId
        })

        return {
          id: updated[0].id,
          apId: (apId || existing.appointment.apId) as string,
          ccId: ccAppointmentData.id
        }
      } else {
        // Create new appointment
        if (!apId) {
          throw new Error('AP ID is required for new appointment creation')
        }

        const created = await this.db
          .insert(appointment)
          .values({
            apId,
            ccId: ccAppointmentData.id,
            apUpdatedAt: new Date(),
            ccUpdatedAt: new Date(ccAppointmentData.updatedAt),
            ccData: ccAppointmentData
          })
          .returning()

        logger.info('New appointment record created', {
          requestId,
          appointmentId: created[0].id,
          ccId: ccAppointmentData.id,
          apId
        })

        return {
          id: created[0].id,
          apId,
          ccId: ccAppointmentData.id
        }
      }

    } catch (error) {
      await ErrorLogger.logError(
        'AppointmentCreateOrUpdate',
        error,
        { ccId: ccAppointmentData.id, apId, requestId },
        'AppointmentService'
      )
      throw error
    }
  }
}

// Export singleton instance
export const appointmentService = new AppointmentService()
```

### 3. Create ProcessCcAppointmentCreate Job
Create `src/jobs/processCcAppointmentCreate.ts`:

```typescript
import { BaseJobProcessor } from './baseJobProcessor'
import { appointmentService } from '@/services/appointmentService'
import { contactSyncService } from '@/services/contactSyncService'
import { apAppointmentRequest } from '@libAP'
import { ccPatientRequest } from '@libCC'
import { logger, getConfig } from '@utils'
import type { JobPayload, JobResult } from '@/types/jobs'
import type { ProcessCcAppointmentCreatePayload, AppointmentSyncResult } from '@/types/appointmentJobs'
import type { PostAPAppointmentType } from '@libAP/APTypes'

export class ProcessCcAppointmentCreateJob extends BaseJobProcessor<ProcessCcAppointmentCreatePayload> {
  protected jobName = 'ProcessCcAppointmentCreate'
  protected entityType = 'appointment' as const
  protected operation = 'create' as const

  protected async executeJob(
    jobPayload: JobPayload<ProcessCcAppointmentCreatePayload>
  ): Promise<JobResult> {
    const { payload, requestId, location } = jobPayload
    const ccAppointmentData = payload

    logger.info('Processing appointment creation', {
      requestId,
      ccAppointmentId: ccAppointmentData.id,
      startsAt: ccAppointmentData.startsAt,
      patients: ccAppointmentData.patients
    })

    // Validate required data
    if (!ccAppointmentData.patients || ccAppointmentData.patients.length === 0) {
      throw new Error('Appointment must have at least one patient')
    }

    // Check if appointment already exists
    const existingAppointment = await appointmentService.findAppointmentByCcId(
      ccAppointmentData.id,
      requestId
    )

    if (existingAppointment.found) {
      logger.info('Appointment already exists', {
        requestId,
        ccAppointmentId: ccAppointmentData.id,
        apId: existingAppointment.appointment?.apId
      })

      return {
        success: true,
        message: 'Appointment already exists in AP',
        data: {
          appointmentId: existingAppointment.appointment.id,
          apId: existingAppointment.appointment.apId,
          ccId: ccAppointmentData.id,
          action: 'existing'
        }
      }
    }

    // Get and sync patients
    const patientSyncResult = await this.syncAppointmentPatients(
      ccAppointmentData.patients,
      requestId
    )

    if (patientSyncResult.linkedPatients.length === 0) {
      throw new Error('No patients could be linked for appointment')
    }

    // Create appointment in AP
    const apAppointment = await this.createAppointmentInAP(
      ccAppointmentData,
      patientSyncResult.linkedPatients,
      requestId
    )

    // Save appointment record in database
    const appointmentRecord = await appointmentService.createOrUpdateAppointment(
      ccAppointmentData,
      apAppointment.id,
      requestId
    )

    logger.info('Appointment creation completed', {
      requestId,
      appointmentId: appointmentRecord.id,
      apId: apAppointment.id,
      ccId: ccAppointmentData.id,
      patientsLinked: patientSyncResult.linkedPatients.length
    })

    return {
      success: true,
      message: 'Appointment created successfully in AP',
      data: {
        appointmentId: appointmentRecord.id,
        apId: apAppointment.id,
        ccId: ccAppointmentData.id,
        action: 'created',
        patientsLinked: patientSyncResult.linkedPatients.length,
        servicesLinked: ccAppointmentData.services?.length || 0
      }
    }
  }

  /**
   * Sync patients for appointment
   */
  private async syncAppointmentPatients(
    ccPatientIds: number[],
    requestId: string
  ): Promise<{ linkedPatients: Array<{ ccId: number; apId: string }> }> {
    const linkedPatients: Array<{ ccId: number; apId: string }> = []

    // Get existing linked patients
    const existingPatients = await appointmentService.getLinkedPatients(ccPatientIds, requestId)
    
    for (const existingPatient of existingPatients) {
      linkedPatients.push({
        ccId: existingPatient.ccId,
        apId: existingPatient.apId
      })
    }

    // Find patients that need to be synced
    const missingPatientIds = ccPatientIds.filter(
      ccId => !existingPatients.find(p => p.ccId === ccId)
    )

    // Sync missing patients
    for (const ccPatientId of missingPatientIds) {
      try {
        const ccPatient = await ccPatientRequest.getPatient(ccPatientId)
        if (ccPatient) {
          const syncResult = await contactSyncService.updateOrCreateContact(
            ccPatient,
            { syncCustomFields: false }, // Skip custom fields for appointment creation
            requestId
          )

          if (syncResult.success) {
            linkedPatients.push({
              ccId: ccPatientId,
              apId: syncResult.contact.apId
            })

            logger.info('Patient synced for appointment', {
              requestId,
              ccPatientId,
              apId: syncResult.contact.apId
            })
          }
        }
      } catch (error) {
        logger.warn('Failed to sync patient for appointment', {
          requestId,
          ccPatientId,
          error: error instanceof Error ? error.message : String(error)
        })
      }
    }

    return { linkedPatients }
  }

  /**
   * Create appointment in AP
   */
  private async createAppointmentInAP(
    ccAppointmentData: ProcessCcAppointmentCreatePayload,
    linkedPatients: Array<{ ccId: number; apId: string }>,
    requestId: string
  ): Promise<any> {
    const primaryPatient = linkedPatients[0]
    
    const appointmentPayload: PostAPAppointmentType = {
      contactId: primaryPatient.apId,
      startTime: ccAppointmentData.startsAt,
      endTime: ccAppointmentData.endsAt,
      title: ccAppointmentData.title || 'CC Appointment',
      notes: this.buildAppointmentNotes(ccAppointmentData, linkedPatients),
      status: ccAppointmentData.canceledAt ? 'cancelled' : 'scheduled'
    }

    // Add service mapping (placeholder for now)
    if (ccAppointmentData.services && ccAppointmentData.services.length > 0) {
      // TODO: Implement service mapping in Sprint 3 Task 5
      appointmentPayload.serviceId = ccAppointmentData.services[0].toString()
    }

    const locationId = getConfig('locationID') as string
    const apAppointment = await apAppointmentRequest.create(appointmentPayload, locationId)

    if (!apAppointment || !apAppointment.id) {
      throw new Error('Failed to create appointment in AP - no appointment ID returned')
    }

    logger.info('Appointment created in AP', {
      requestId,
      apAppointmentId: apAppointment.id,
      ccAppointmentId: ccAppointmentData.id,
      contactId: primaryPatient.apId
    })

    return apAppointment
  }

  /**
   * Build appointment notes with CC data
   */
  private buildAppointmentNotes(
    ccAppointmentData: ProcessCcAppointmentCreatePayload,
    linkedPatients: Array<{ ccId: number; apId: string }>
  ): string {
    const notes = [`CC Appointment ID: ${ccAppointmentData.id}`]
    
    if (ccAppointmentData.title) {
      notes.push(`Title: ${ccAppointmentData.title}`)
    }

    if (linkedPatients.length > 1) {
      notes.push(`Additional Patients: ${linkedPatients.slice(1).map(p => p.apId).join(', ')}`)
    }

    if (ccAppointmentData.firstOfPatient) {
      notes.push('First appointment for patient')
    }

    return notes.join('\n')
  }

  protected isRetryableError(error: unknown): boolean {
    if (error instanceof Error) {
      // Retry on network errors or AP API errors
      return error.message.includes('fetch') ||
             error.message.includes('timeout') ||
             error.message.includes('AP API') ||
             error.message.includes('rate limit')
    }
    return false
  }
}

// Export job processor function for use in job factory
export const processCcAppointmentCreate = async (
  jobPayload: JobPayload<ProcessCcAppointmentCreatePayload>
): Promise<JobResult> => {
  const processor = new ProcessCcAppointmentCreateJob()
  return processor.process(jobPayload)
}
```

### 4. Update Job Factory
Update `src/jobs/jobFactory.ts`:

```typescript
import { processCcAppointmentCreate } from './processCcAppointmentCreate'
import type { ProcessCcAppointmentCreatePayload } from '@/types/appointmentJobs'

export class JobFactory {
  // ... existing methods

  async processCcAppointmentCreate(
    jobPayload: JobPayload<ProcessCcAppointmentCreatePayload>
  ): Promise<JobResult> {
    return processCcAppointmentCreate(jobPayload)
  }
}
```

### 5. Update Event Router
Update `src/webhooks/eventRouter.ts`:

```typescript
import type { ProcessCcAppointmentCreatePayload } from '@/types/appointmentJobs'

private async handleAppointmentCreated(
  event: CCWebhookEvent,
  location: string,
  requestId: string
): Promise<EventProcessingResult> {
  return jobFactory.processCcAppointmentCreate({
    payload: event.payload as ProcessCcAppointmentCreatePayload,
    location,
    requestId,
    eventId: event.id
  })
}
```

## Dependencies
- **Prerequisite**: Sprint 2 (Patient sync functionality)
- **Integrates with**: Appointment service, contact sync service, AP appointment request layer
- **Blocks**: Task 2 (ProcessCcAppointmentUpdate)

## Testing Strategy
```typescript
// Test file: src/jobs/__tests__/processCcAppointmentCreate.test.ts
describe('ProcessCcAppointmentCreate', () => {
  test('should create appointment with linked patients', async () => {
    const payload: ProcessCcAppointmentCreatePayload = {
      id: 456,
      startsAt: '2023-01-01T10:00:00Z',
      endsAt: '2023-01-01T11:00:00Z',
      patients: [123],
      title: 'Test Appointment',
      updatedAt: '2023-01-01T00:00:00Z',
      createdAt: '2023-01-01T00:00:00Z'
    }
    
    const result = await processCcAppointmentCreate({
      payload,
      location: 'test-clinic',
      requestId: 'test-request',
      eventId: 456
    })
    
    expect(result.success).toBe(true)
    expect(result.data?.apId).toBeDefined()
  })
})
```

## Implementation Notes
- Follow the exact logic from legacy ProcessCcAppointmentCreate job
- Integrate with patient sync to ensure all patients are linked
- Use existing AP appointment request layer for API calls
- Implement proper service mapping (placeholder for Sprint 3 Task 5)
- Add comprehensive logging for debugging
- Handle multiple patients per appointment correctly
