# Task 1: Port ProcessApAppointmentCreatesController

## Description
Implement AP appointment creation webhook handler with CC sync. This ports the legacy `ProcessApAppointmentCreates<PERSON>ontroller` to handle appointment creation events from the AP platform and sync them to CC.

## Acceptance Criteria
- [ ] AP appointment creation webhook handler implemented
- [ ] AP appointment data transformation to CC format
- [ ] Patient linking and contact sync integration
- [ ] Bidirectional sync prevention logic
- [ ] Never use `any` or `as any` - use proper TypeScript types
- [ ] Comprehensive error handling and logging

## Technical Implementation Details

### 1. Create AP Webhook Types
Create `src/types/apWebhooks.ts`:

```typescript
export interface APAppointmentWebhookPayload {
  calendar: {
    id: string
    appointmentId: string
    startTime: string
    endTime: string
    status: string
    created_by_meta: {
      source: string
      channel?: string
    }
  }
  contact_id: string
  email?: string
  phone?: string
  firstName?: string
  lastName?: string
}

export interface APWebhookContext {
  location: string
  requestId: string
  apAppointmentData: APAppointmentWebhookPayload
  skipCCSync?: boolean
}

export interface APAppointmentSyncResult {
  success: boolean
  action: 'created' | 'updated' | 'skipped' | 'error'
  appointment?: {
    id: string
    apId: string
    ccId?: number
  }
  ccAppointment?: any
  patientLinked: boolean
  message: string
}
```

### 2. Create AP Webhook Service
Create `src/services/apWebhookService.ts`:

```typescript
import { appointmentService } from './appointmentService'
import { patientService } from './patientService'
import { ccPatientSyncService } from './ccPatientSyncService'
import { ccAppointmentRequest } from '@libCC'
import { apAppointmentRequest } from '@libAP'
import { skipLogic } from './skipLogic'
import { logger, ErrorLogger } from '@utils'
import type { APAppointmentWebhookPayload, APAppointmentSyncResult } from '@/types/apWebhooks'
import type { PostCCAppointmentType } from '@libCC/CCTypes'

export class APWebhookService {
  /**
   * Process AP appointment creation webhook
   */
  async processAppointmentCreate(
    apWebhookData: APAppointmentWebhookPayload,
    location: string,
    requestId: string
  ): Promise<APAppointmentSyncResult> {
    try {
      logger.info('Processing AP appointment creation', {
        requestId,
        apAppointmentId: apWebhookData.calendar.appointmentId,
        contactId: apWebhookData.contact_id,
        startTime: apWebhookData.calendar.startTime
      })

      // Check if this appointment was created by our CC sync to prevent loops
      if (this.isCreatedByCC(apWebhookData)) {
        logger.info('Appointment created by CC sync, skipping reverse sync', {
          requestId,
          apAppointmentId: apWebhookData.calendar.appointmentId,
          source: apWebhookData.calendar.created_by_meta.source
        })

        return {
          success: true,
          action: 'skipped',
          patientLinked: false,
          message: 'Appointment created by CC sync, skipping reverse sync'
        }
      }

      // Check skip logic to prevent duplicate processing
      const shouldSkip = await skipLogic.hasProcessAppointment(
        apWebhookData.calendar.appointmentId,
        'create',
        location
      )

      if (shouldSkip) {
        return {
          success: true,
          action: 'skipped',
          patientLinked: false,
          message: 'Appointment creation already processed recently'
        }
      }

      // Check if appointment already exists in our database
      const existingAppointment = await this.findAppointmentByApId(
        apWebhookData.calendar.appointmentId,
        requestId
      )

      if (existingAppointment) {
        logger.info('Appointment already exists in database', {
          requestId,
          apAppointmentId: apWebhookData.calendar.appointmentId,
          ccId: existingAppointment.ccId
        })

        return {
          success: true,
          action: 'skipped',
          appointment: existingAppointment,
          patientLinked: true,
          message: 'Appointment already exists'
        }
      }

      // Find or create linked patient
      const patientLinkResult = await this.findOrCreateLinkedPatient(
        apWebhookData.contact_id,
        apWebhookData,
        location,
        requestId
      )

      if (!patientLinkResult.success || !patientLinkResult.ccPatientId) {
        throw new Error(`Failed to link patient: ${patientLinkResult.message}`)
      }

      // Create appointment in CC
      const ccAppointment = await this.createAppointmentInCC(
        apWebhookData,
        patientLinkResult.ccPatientId,
        requestId
      )

      // Save appointment mapping in database
      const appointmentRecord = await appointmentService.createOrUpdateAppointment(
        ccAppointment,
        apWebhookData.calendar.appointmentId,
        requestId
      )

      // Record processing in skip logic
      await skipLogic.recordProcessing(
        'appointment',
        apWebhookData.calendar.appointmentId,
        'create',
        location,
        requestId
      )

      logger.info('AP appointment creation completed', {
        requestId,
        appointmentId: appointmentRecord.id,
        apId: apWebhookData.calendar.appointmentId,
        ccId: ccAppointment.id
      })

      return {
        success: true,
        action: 'created',
        appointment: {
          id: appointmentRecord.id,
          apId: apWebhookData.calendar.appointmentId,
          ccId: ccAppointment.id
        },
        ccAppointment,
        patientLinked: true,
        message: 'Appointment created successfully in CC'
      }

    } catch (error) {
      await ErrorLogger.logError(
        'APAppointmentCreate',
        error,
        {
          apAppointmentId: apWebhookData.calendar.appointmentId,
          contactId: apWebhookData.contact_id,
          location,
          requestId
        },
        'APWebhookService'
      )

      return {
        success: false,
        action: 'error',
        patientLinked: false,
        message: error instanceof Error ? error.message : 'Unknown error processing AP appointment'
      }
    }
  }

  /**
   * Check if appointment was created by CC sync to prevent loops
   */
  private isCreatedByCC(apWebhookData: APAppointmentWebhookPayload): boolean {
    const source = apWebhookData.calendar.created_by_meta?.source
    const channel = apWebhookData.calendar.created_by_meta?.channel

    // Check for CC sync indicators
    return source === 'cc' || 
           source === 'cc_api' || 
           channel === 'cc_sync' ||
           source?.includes('cc')
  }

  /**
   * Find appointment by AP ID in database
   */
  private async findAppointmentByApId(
    apAppointmentId: string,
    requestId: string
  ): Promise<{ id: string; apId: string; ccId: number } | null> {
    try {
      // This would need to be implemented in appointmentService
      // For now, return null (not found)
      return null
    } catch (error) {
      logger.warn('Error finding appointment by AP ID', {
        requestId,
        apAppointmentId,
        error: error instanceof Error ? error.message : String(error)
      })
      return null
    }
  }

  /**
   * Find or create linked patient for appointment
   */
  private async findOrCreateLinkedPatient(
    apContactId: string,
    apWebhookData: APAppointmentWebhookPayload,
    location: string,
    requestId: string
  ): Promise<{ success: boolean; ccPatientId?: number; message: string }> {
    try {
      // First check if we have this contact in our database
      const existingPatient = await patientService.searchContact(
        0, // No CC ID to search by
        apWebhookData.email,
        apWebhookData.phone,
        requestId
      )

      if (existingPatient.found && existingPatient.contact?.ccId) {
        logger.info('Found existing patient mapping', {
          requestId,
          apContactId,
          ccPatientId: existingPatient.contact.ccId
        })

        return {
          success: true,
          ccPatientId: existingPatient.contact.ccId,
          message: 'Patient already linked'
        }
      }

      // Get full AP contact data
      const apContact = await apAppointmentRequest.getContact(apContactId)
      
      if (!apContact) {
        throw new Error(`AP contact not found: ${apContactId}`)
      }

      // Create or find patient in CC
      const ccPatientResult = await ccPatientSyncService.createPatientToCC({
        apContact,
        location,
        requestId
      })

      if (!ccPatientResult.success || !ccPatientResult.patient) {
        throw new Error(`Failed to create patient in CC: ${ccPatientResult.message}`)
      }

      logger.info('Patient linked for AP appointment', {
        requestId,
        apContactId,
        ccPatientId: ccPatientResult.patient.id,
        action: ccPatientResult.action
      })

      return {
        success: true,
        ccPatientId: ccPatientResult.patient.id,
        message: `Patient ${ccPatientResult.action} in CC`
      }

    } catch (error) {
      logger.error('Failed to link patient for AP appointment', {
        requestId,
        apContactId,
        error: error instanceof Error ? error.message : String(error)
      })

      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error linking patient'
      }
    }
  }

  /**
   * Create appointment in CC
   */
  private async createAppointmentInCC(
    apWebhookData: APAppointmentWebhookPayload,
    ccPatientId: number,
    requestId: string
  ): Promise<any> {
    const appointmentPayload: PostCCAppointmentType = {
      title: 'AP Appointment',
      startsAt: apWebhookData.calendar.startTime,
      endsAt: apWebhookData.calendar.endTime,
      patients: [ccPatientId],
      notes: this.buildAppointmentNotes(apWebhookData),
      status: this.mapAPStatusToCC(apWebhookData.calendar.status)
    }

    logger.info('Creating appointment in CC', {
      requestId,
      apAppointmentId: apWebhookData.calendar.appointmentId,
      ccPatientId,
      startsAt: appointmentPayload.startsAt
    })

    const ccAppointment = await ccAppointmentRequest.create(appointmentPayload)

    if (!ccAppointment || !ccAppointment.id) {
      throw new Error('Failed to create appointment in CC - no appointment ID returned')
    }

    logger.info('Appointment created in CC', {
      requestId,
      ccAppointmentId: ccAppointment.id,
      apAppointmentId: apWebhookData.calendar.appointmentId
    })

    return ccAppointment
  }

  /**
   * Build appointment notes with AP data
   */
  private buildAppointmentNotes(apWebhookData: APAppointmentWebhookPayload): string {
    const notes = [
      `AP Appointment ID: ${apWebhookData.calendar.appointmentId}`,
      `AP Contact ID: ${apWebhookData.contact_id}`
    ]

    if (apWebhookData.calendar.created_by_meta?.source) {
      notes.push(`Source: ${apWebhookData.calendar.created_by_meta.source}`)
    }

    return notes.join('\n')
  }

  /**
   * Map AP appointment status to CC status
   */
  private mapAPStatusToCC(apStatus: string): string {
    const statusMap: Record<string, string> = {
      'scheduled': 'scheduled',
      'confirmed': 'confirmed',
      'cancelled': 'cancelled',
      'completed': 'completed',
      'no-show': 'no_show'
    }

    return statusMap[apStatus.toLowerCase()] || 'scheduled'
  }
}

// Export singleton instance
export const apWebhookService = new APWebhookService()
```

### 3. Create AP Webhook Controller
Create `src/controllers/apWebhookController.ts`:

```typescript
import type { Context } from 'hono'
import { apWebhookService } from '@/services/apWebhookService'
import { logger } from '@utils'
import type { APAppointmentWebhookPayload } from '@/types/apWebhooks'

export class APWebhookController {
  /**
   * Handle AP appointment creation webhook
   */
  async handleAppointmentCreate(c: Context): Promise<Response> {
    const requestId = crypto.randomUUID()
    const location = c.req.param('location')
    
    try {
      const body = await c.req.json() as APAppointmentWebhookPayload
      
      logger.info('AP appointment creation webhook received', {
        requestId,
        location,
        apAppointmentId: body.calendar.appointmentId,
        contactId: body.contact_id
      })

      const result = await apWebhookService.processAppointmentCreate(
        body,
        location,
        requestId
      )

      return c.json({
        success: result.success,
        message: result.message,
        requestId,
        processedAt: new Date().toISOString(),
        data: {
          action: result.action,
          apAppointmentId: body.calendar.appointmentId,
          ccAppointmentId: result.appointment?.ccId,
          patientLinked: result.patientLinked
        }
      }, result.success ? 200 : 500)

    } catch (error) {
      logger.error('AP appointment webhook processing failed', {
        requestId,
        location,
        error: error instanceof Error ? error.message : String(error)
      })

      return c.json({
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        requestId,
        processedAt: new Date().toISOString()
      }, 500)
    }
  }

  /**
   * Handle AP appointment update webhook
   */
  async handleAppointmentUpdate(c: Context): Promise<Response> {
    // Will be implemented in Task 2
    return c.json({
      success: true,
      message: 'AP appointment update - placeholder implementation'
    })
  }

  /**
   * Handle AP appointment deletion webhook
   */
  async handleAppointmentDelete(c: Context): Promise<Response> {
    // Will be implemented in Task 3
    return c.json({
      success: true,
      message: 'AP appointment delete - placeholder implementation'
    })
  }
}

// Export singleton instance
export const apWebhookController = new APWebhookController()
```

### 4. Update Main Application Routes
Update `src/index.ts` to include AP webhook routes:

```typescript
import { apWebhookController } from './controllers/apWebhookController'
import { webhookValidator } from './middlewares/webhookValidator'

// AP webhook endpoints
app.post('/webhooks/ap/:location/appointment/create', 
  webhookValidator,
  (c) => apWebhookController.handleAppointmentCreate(c)
)

app.post('/webhooks/ap/:location/appointment/update',
  webhookValidator,
  (c) => apWebhookController.handleAppointmentUpdate(c)
)

app.post('/webhooks/ap/:location/appointment/delete',
  webhookValidator,
  (c) => apWebhookController.handleAppointmentDelete(c)
)
```

### 5. Update Webhook Validator
Update `src/middlewares/webhookValidator.ts` to handle AP webhooks:

```typescript
// Add AP webhook schema validation
export const APWebhookEventSchema = z.object({
  calendar: z.object({
    id: z.string(),
    appointmentId: z.string(),
    startTime: z.string(),
    endTime: z.string(),
    status: z.string(),
    created_by_meta: z.object({
      source: z.string(),
      channel: z.string().optional()
    })
  }),
  contact_id: z.string(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional()
})

// Update validator to handle AP webhooks based on route
```

## Dependencies
- **Prerequisite**: Sprint 2 (Patient sync), Sprint 3 (Appointment sync), Sprint 4 (Custom fields)
- **Integrates with**: CC patient sync service, appointment service, skip logic
- **Blocks**: Task 2 (AP appointment updates), Task 3 (AP appointment deletes)

## Testing Strategy
```typescript
// Test file: src/controllers/__tests__/apWebhookController.test.ts
describe('APWebhookController', () => {
  test('should process AP appointment creation', async () => {
    const payload: APAppointmentWebhookPayload = {
      calendar: {
        id: 'cal-123',
        appointmentId: 'apt-456',
        startTime: '2023-01-01T10:00:00Z',
        endTime: '2023-01-01T11:00:00Z',
        status: 'scheduled',
        created_by_meta: {
          source: 'manual'
        }
      },
      contact_id: 'contact-789',
      email: '<EMAIL>'
    }
    
    const response = await app.request('/webhooks/ap/test-clinic/appointment/create', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    })
    
    expect(response.status).toBe(200)
    const data = await response.json()
    expect(data.success).toBe(true)
  })
})
```

## Implementation Notes
- Port exact logic from legacy ProcessApAppointmentCreatesController
- Implement bidirectional sync prevention to avoid loops
- Use comprehensive patient linking logic
- Add proper error handling for CC API failures
- Integrate with skip logic to prevent duplicate processing
- Follow existing project patterns for controller organization
