# Task 1: Port ProcessCcInvoice Job

## Description
Implement invoice processing job with custom field calculations and AP sync. This ports the legacy `ProcessCcInvoice` job to handle invoice creation events and sync financial data to AP custom fields.

## Acceptance Criteria
- [ ] Invoice processing job processor implemented using BaseJobProcessor
- [ ] Invoice data transformation and validation
- [ ] Custom field sync for invoice data
- [ ] Patient linking and financial calculations
- [ ] Never use `any` or `as any` - use proper TypeScript types
- [ ] Comprehensive error handling and logging

## Technical Implementation Details

### 1. Create Invoice Job Types
Create `src/types/invoiceJobs.ts`:

```typescript
export interface ProcessCcInvoicePayload {
  id: number
  patient: number
  pdfUrl?: string
  discount?: number
  status: string
  positions: Array<{
    name: string
    gross: number
    discount?: number
  }>
  diagnoses?: Array<{
    text: string
  }>
  practicioner?: number
  updatedAt: string
  createdAt: string
}

export interface InvoiceProcessingContext {
  ccInvoiceData: ProcessCcInvoicePayload
  linkedPatient?: {
    id: string
    apId: string
    ccId: number
  }
  location: string
  requestId: string
}

export interface InvoiceProcessingResult {
  success: boolean
  action: 'processed' | 'skipped' | 'error'
  customFieldsUpdated: boolean
  financialDataSynced: boolean
  totalAmount: number
  discountAmount: number
  message: string
}

export interface InvoiceCustomFieldData {
  totalInvoices: number
  totalInvoiceAmount: number
  lastInvoiceDate: string
  lastInvoiceAmount: number
  averageInvoiceAmount: number
  totalDiscount: number
}
```

### 2. Create Invoice Service
Create `src/services/invoiceService.ts`:

```typescript
import { getDb } from '@database'
import { patient } from '@database/schema'
import { eq } from 'drizzle-orm'
import { logger, ErrorLogger } from '@utils'
import { ccInvoiceRequest } from '@libCC'
import type { ProcessCcInvoicePayload, InvoiceCustomFieldData } from '@/types/invoiceJobs'

export class InvoiceService {
  private db = getDb()

  /**
   * Calculate invoice totals and financial metrics
   */
  calculateInvoiceMetrics(
    invoiceData: ProcessCcInvoicePayload
  ): { totalAmount: number; discountAmount: number; netAmount: number } {
    let totalAmount = 0
    let discountAmount = invoiceData.discount || 0

    // Calculate total from positions
    for (const position of invoiceData.positions) {
      totalAmount += position.gross
      if (position.discount) {
        discountAmount += position.discount
      }
    }

    const netAmount = totalAmount - discountAmount

    return {
      totalAmount,
      discountAmount,
      netAmount
    }
  }

  /**
   * Get patient's invoice history for custom field calculations
   */
  async getPatientInvoiceHistory(
    ccPatientId: number,
    requestId?: string
  ): Promise<ProcessCcInvoicePayload[]> {
    try {
      // Get all invoices for the patient from CC
      const invoices = await ccInvoiceRequest.getPatientInvoices(ccPatientId)
      
      logger.info('Retrieved patient invoice history', {
        requestId,
        ccPatientId,
        invoiceCount: invoices.length
      })

      return invoices
    } catch (error) {
      await ErrorLogger.logError(
        'GetPatientInvoiceHistory',
        error,
        { ccPatientId, requestId },
        'InvoiceService'
      )

      return []
    }
  }

  /**
   * Calculate custom field data for patient based on invoice history
   */
  async calculateInvoiceCustomFields(
    ccPatientId: number,
    currentInvoice: ProcessCcInvoicePayload,
    requestId?: string
  ): Promise<InvoiceCustomFieldData> {
    try {
      const invoiceHistory = await this.getPatientInvoiceHistory(ccPatientId, requestId)
      
      // Include current invoice in calculations
      const allInvoices = [...invoiceHistory, currentInvoice]
      
      let totalAmount = 0
      let totalDiscount = 0
      let lastInvoiceDate = currentInvoice.createdAt
      let lastInvoiceAmount = 0

      for (const invoice of allInvoices) {
        const metrics = this.calculateInvoiceMetrics(invoice)
        totalAmount += metrics.totalAmount
        totalDiscount += metrics.discountAmount

        // Find most recent invoice
        if (new Date(invoice.createdAt) >= new Date(lastInvoiceDate)) {
          lastInvoiceDate = invoice.createdAt
          lastInvoiceAmount = metrics.totalAmount
        }
      }

      const averageInvoiceAmount = allInvoices.length > 0 ? totalAmount / allInvoices.length : 0

      const customFieldData: InvoiceCustomFieldData = {
        totalInvoices: allInvoices.length,
        totalInvoiceAmount: totalAmount,
        lastInvoiceDate,
        lastInvoiceAmount,
        averageInvoiceAmount,
        totalDiscount
      }

      logger.info('Invoice custom fields calculated', {
        requestId,
        ccPatientId,
        customFieldData
      })

      return customFieldData

    } catch (error) {
      await ErrorLogger.logError(
        'CalculateInvoiceCustomFields',
        error,
        { ccPatientId, currentInvoiceId: currentInvoice.id, requestId },
        'InvoiceService'
      )

      // Return default values on error
      const currentMetrics = this.calculateInvoiceMetrics(currentInvoice)
      return {
        totalInvoices: 1,
        totalInvoiceAmount: currentMetrics.totalAmount,
        lastInvoiceDate: currentInvoice.createdAt,
        lastInvoiceAmount: currentMetrics.totalAmount,
        averageInvoiceAmount: currentMetrics.totalAmount,
        totalDiscount: currentMetrics.discountAmount
      }
    }
  }

  /**
   * Format diagnoses for custom field storage
   */
  formatDiagnoses(diagnoses?: Array<{ text: string }>): string {
    if (!diagnoses || diagnoses.length === 0) {
      return ''
    }

    return diagnoses.map(d => d.text).join('; ')
  }

  /**
   * Format invoice positions for notes
   */
  formatInvoicePositions(positions: Array<{ name: string; gross: number; discount?: number }>): string {
    return positions.map(pos => {
      const discount = pos.discount ? ` (Discount: ${pos.discount})` : ''
      return `${pos.name}: ${pos.gross}${discount}`
    }).join('\n')
  }
}

// Export singleton instance
export const invoiceService = new InvoiceService()
```

### 3. Create ProcessCcInvoice Job
Create `src/jobs/processCcInvoice.ts`:

```typescript
import { BaseJobProcessor } from './baseJobProcessor'
import { invoiceService } from '@/services/invoiceService'
import { patientService } from '@/services/patientService'
import { customFieldSyncService } from '@/services/customFieldSyncService'
import { logger } from '@utils'
import type { JobPayload, JobResult } from '@/types/jobs'
import type { ProcessCcInvoicePayload, InvoiceProcessingResult } from '@/types/invoiceJobs'

export class ProcessCcInvoiceJob extends BaseJobProcessor<ProcessCcInvoicePayload> {
  protected jobName = 'ProcessCcInvoice'
  protected entityType = 'invoice' as const
  protected operation = 'create' as const

  protected async executeJob(
    jobPayload: JobPayload<ProcessCcInvoicePayload>
  ): Promise<JobResult> {
    const { payload, requestId, location } = jobPayload
    const ccInvoiceData = payload

    logger.info('Processing invoice', {
      requestId,
      ccInvoiceId: ccInvoiceData.id,
      ccPatientId: ccInvoiceData.patient,
      status: ccInvoiceData.status
    })

    // Find linked patient
    const linkedPatient = await this.findLinkedPatient(ccInvoiceData.patient, requestId)
    
    if (!linkedPatient) {
      throw new Error(`No linked patient found for CC patient ID: ${ccInvoiceData.patient}`)
    }

    // Calculate invoice metrics
    const invoiceMetrics = invoiceService.calculateInvoiceMetrics(ccInvoiceData)
    
    logger.info('Invoice metrics calculated', {
      requestId,
      ccInvoiceId: ccInvoiceData.id,
      totalAmount: invoiceMetrics.totalAmount,
      discountAmount: invoiceMetrics.discountAmount,
      netAmount: invoiceMetrics.netAmount
    })

    // Calculate custom field data
    const customFieldData = await invoiceService.calculateInvoiceCustomFields(
      ccInvoiceData.patient,
      ccInvoiceData,
      requestId
    )

    // Sync custom fields to AP
    const customFieldsUpdated = await this.syncInvoiceCustomFields(
      linkedPatient.apId,
      customFieldData,
      ccInvoiceData,
      requestId
    )

    // Sync financial data (placeholder for comprehensive financial sync)
    const financialDataSynced = await this.syncFinancialData(
      linkedPatient.apId,
      ccInvoiceData,
      invoiceMetrics,
      requestId
    )

    const result: InvoiceProcessingResult = {
      success: true,
      action: 'processed',
      customFieldsUpdated,
      financialDataSynced,
      totalAmount: invoiceMetrics.totalAmount,
      discountAmount: invoiceMetrics.discountAmount,
      message: 'Invoice processed successfully'
    }

    logger.info('Invoice processing completed', {
      requestId,
      ccInvoiceId: ccInvoiceData.id,
      apContactId: linkedPatient.apId,
      result
    })

    return {
      success: true,
      message: 'Invoice processed and synced to AP',
      data: {
        ccInvoiceId: ccInvoiceData.id,
        apContactId: linkedPatient.apId,
        totalAmount: invoiceMetrics.totalAmount,
        customFieldsUpdated,
        financialDataSynced,
        action: 'processed'
      }
    }
  }

  /**
   * Find linked patient for invoice
   */
  private async findLinkedPatient(
    ccPatientId: number,
    requestId: string
  ): Promise<{ id: string; apId: string; ccId: number } | null> {
    const searchResult = await patientService.searchContact(
      ccPatientId,
      undefined,
      undefined,
      requestId
    )

    if (searchResult.found && searchResult.contact?.apId) {
      return {
        id: searchResult.contact.id,
        apId: searchResult.contact.apId,
        ccId: ccPatientId
      }
    }

    return null
  }

  /**
   * Sync invoice data to AP custom fields
   */
  private async syncInvoiceCustomFields(
    apContactId: string,
    customFieldData: any,
    invoiceData: ProcessCcInvoicePayload,
    requestId: string
  ): Promise<boolean> {
    try {
      // Prepare custom field updates
      const customFieldUpdates = {
        'Total Invoices': customFieldData.totalInvoices.toString(),
        'Total Invoice Amount': customFieldData.totalInvoiceAmount.toString(),
        'Last Invoice Date': customFieldData.lastInvoiceDate,
        'Last Invoice Amount': customFieldData.lastInvoiceAmount.toString(),
        'Average Invoice Amount': customFieldData.averageInvoiceAmount.toFixed(2),
        'Total Discount': customFieldData.totalDiscount.toString(),
        'Last Invoice Status': invoiceData.status
      }

      // Add diagnoses if available
      if (invoiceData.diagnoses && invoiceData.diagnoses.length > 0) {
        customFieldUpdates['Last Diagnoses'] = invoiceService.formatDiagnoses(invoiceData.diagnoses)
      }

      // Sync to AP using custom field service (will be implemented in Task 3)
      const syncResult = await customFieldSyncService.syncCCtoAPCustomfields(
        apContactId,
        customFieldUpdates,
        requestId
      )

      logger.info('Invoice custom fields synced', {
        requestId,
        apContactId,
        ccInvoiceId: invoiceData.id,
        fieldsUpdated: Object.keys(customFieldUpdates).length,
        success: syncResult.success
      })

      return syncResult.success

    } catch (error) {
      logger.error('Failed to sync invoice custom fields', {
        requestId,
        apContactId,
        ccInvoiceId: invoiceData.id,
        error: error instanceof Error ? error.message : String(error)
      })

      return false
    }
  }

  /**
   * Sync financial data (placeholder for comprehensive sync)
   */
  private async syncFinancialData(
    apContactId: string,
    invoiceData: ProcessCcInvoicePayload,
    metrics: any,
    requestId: string
  ): Promise<boolean> {
    try {
      logger.info('Financial data sync requested', {
        requestId,
        apContactId,
        ccInvoiceId: invoiceData.id,
        totalAmount: metrics.totalAmount
      })

      // TODO: Implement comprehensive financial data sync
      // This could include:
      // - Creating AP invoice records
      // - Updating payment tracking
      // - Syncing to accounting systems
      
      return true

    } catch (error) {
      logger.error('Financial data sync failed', {
        requestId,
        apContactId,
        error: error instanceof Error ? error.message : String(error)
      })

      return false
    }
  }

  protected isRetryableError(error: unknown): boolean {
    if (error instanceof Error) {
      // Retry on network errors or API errors
      return error.message.includes('fetch') ||
             error.message.includes('timeout') ||
             error.message.includes('API') ||
             error.message.includes('rate limit')
    }
    return false
  }
}

// Export job processor function for use in job factory
export const processCcInvoice = async (
  jobPayload: JobPayload<ProcessCcInvoicePayload>
): Promise<JobResult> => {
  const processor = new ProcessCcInvoiceJob()
  return processor.process(jobPayload)
}
```

### 4. Update Job Factory
Update `src/jobs/jobFactory.ts`:

```typescript
import { processCcInvoice } from './processCcInvoice'
import type { ProcessCcInvoicePayload } from '@/types/invoiceJobs'

export class JobFactory {
  // ... existing methods

  async processCcInvoice(
    jobPayload: JobPayload<ProcessCcInvoicePayload>
  ): Promise<JobResult> {
    return processCcInvoice(jobPayload)
  }
}
```

### 5. Update Event Router
Update `src/webhooks/eventRouter.ts`:

```typescript
import type { ProcessCcInvoicePayload } from '@/types/invoiceJobs'

private async handleEntityCreated(
  event: CCWebhookEvent,
  location: string,
  requestId: string
): Promise<EventProcessingResult> {
  switch (event.model) {
    case 'Invoice':
      return jobFactory.processCcInvoice({
        payload: event.payload as ProcessCcInvoicePayload,
        location,
        requestId,
        eventId: event.id
      })
    
    // ... other cases
  }
}
```

## Dependencies
- **Prerequisite**: Sprint 2 (Patient sync), Sprint 3 (Appointment sync)
- **Integrates with**: Custom field sync service (Task 3), invoice service
- **Blocks**: Task 2 (ProcessCcPayment)

## Testing Strategy
```typescript
// Test file: src/jobs/__tests__/processCcInvoice.test.ts
describe('ProcessCcInvoice', () => {
  test('should process invoice and sync custom fields', async () => {
    const payload: ProcessCcInvoicePayload = {
      id: 789,
      patient: 123,
      status: 'paid',
      positions: [
        { name: 'Consultation', gross: 100 },
        { name: 'Treatment', gross: 200, discount: 20 }
      ],
      discount: 10,
      updatedAt: '2023-01-01T00:00:00Z',
      createdAt: '2023-01-01T00:00:00Z'
    }
    
    const result = await processCcInvoice({
      payload,
      location: 'test-clinic',
      requestId: 'test-request',
      eventId: 789
    })
    
    expect(result.success).toBe(true)
    expect(result.data?.customFieldsUpdated).toBe(true)
  })
})
```

## Implementation Notes
- Follow the exact logic from legacy ProcessCcInvoice job
- Calculate comprehensive financial metrics for custom fields
- Integrate with custom field sync service (Task 3)
- Handle invoice positions and discounts correctly
- Add comprehensive logging for financial data tracking
- Implement proper error handling for financial calculations
