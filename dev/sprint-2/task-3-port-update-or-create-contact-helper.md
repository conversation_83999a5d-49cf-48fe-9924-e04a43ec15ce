# Task 3: Port updateOrCreateContact Helper

## Description
Implement core contact sync function that transforms CC patient data to AP contact format. This is the central helper function from the legacy system that handles the complex logic of creating or updating contacts in AP.

## Acceptance Criteria
- [ ] Core contact sync logic ported from legacy updateOrCreateContact function
- [ ] CC patient data transformation to AP contact format
- [ ] Contact upsert logic with proper error handling
- [ ] Integration with custom fields sync (placeholder for Sprint 4)
- [ ] Never use `any` or `as any` - use proper TypeScript types
- [ ] Comprehensive logging and error handling

## Technical Implementation Details

### 1. Create Contact Sync Types
Create `src/types/contactSync.ts`:

```typescript
import type { GetCCPatientType } from '@libCC/CCTypes'
import type { GetAPContactType, PostAPContactType } from '@libAP/APTypes'

export interface ContactSyncOptions {
  syncCustomFields?: boolean
  syncFinancialData?: boolean
  forceUpdate?: boolean
  skipDuplicateCheck?: boolean
}

export interface ContactSyncResult {
  success: boolean
  action: 'created' | 'updated' | 'skipped' | 'error'
  contact: {
    id: string
    apId: string
    ccId: number
  }
  apContact?: GetAPContactType
  customFieldsSynced?: boolean
  financialDataSynced?: boolean
  message: string
}

export interface ContactTransformData {
  ccPatientData: GetCCPatientType
  existingApContact?: GetAPContactType
  location: string
  requestId: string
}
```

### 2. Create Contact Sync Service
Create `src/services/contactSyncService.ts`:

```typescript
import { patientService } from './patientService'
import { apContactRequest } from '@libAP'
import { logger, ErrorLogger, getConfig } from '@utils'
import type { GetCCPatientType } from '@libCC/CCTypes'
import type { GetAPContactType, PostAPContactType } from '@libAP/APTypes'
import type { ContactSyncOptions, ContactSyncResult, ContactTransformData } from '@/types/contactSync'

export class ContactSyncService {
  /**
   * Main function to update or create contact - ports legacy updateOrCreateContact
   */
  async updateOrCreateContact(
    ccPatientData: GetCCPatientType,
    options: ContactSyncOptions = {},
    requestId?: string
  ): Promise<ContactSyncResult> {
    const {
      syncCustomFields = true,
      syncFinancialData = true,
      forceUpdate = false,
      skipDuplicateCheck = false
    } = options

    try {
      logger.info('Starting contact sync', {
        requestId,
        ccPatientId: ccPatientData.id,
        email: ccPatientData.email,
        phone: ccPatientData.phoneMobile,
        options
      })

      // Validate required data
      if (!ccPatientData.email && !ccPatientData.phoneMobile) {
        throw new Error('Invalid contact data: email and phone are both missing')
      }

      // Search for existing contact
      const existingContact = await patientService.searchContact(
        ccPatientData.id,
        ccPatientData.email,
        ccPatientData.phoneMobile,
        requestId
      )

      let apContact: GetAPContactType
      let action: 'created' | 'updated' | 'skipped'

      if (existingContact.found && existingContact.contact?.apId && !forceUpdate) {
        // Update existing contact
        const updateResult = await this.updateExistingContact(
          ccPatientData,
          existingContact.contact.apId,
          requestId
        )
        apContact = updateResult.apContact
        action = updateResult.action
      } else {
        // Create new contact
        const createResult = await this.createNewContact(
          ccPatientData,
          requestId
        )
        apContact = createResult.apContact
        action = createResult.action
      }

      // Save/update patient record in database
      const patientRecord = await patientService.createOrUpdatePatient(
        ccPatientData,
        apContact.id,
        requestId
      )

      // Sync custom fields if requested
      let customFieldsSynced = false
      if (syncCustomFields) {
        customFieldsSynced = await this.syncCustomFields(
          ccPatientData,
          apContact.id,
          requestId
        )
      }

      // Sync financial data if requested
      let financialDataSynced = false
      if (syncFinancialData) {
        financialDataSynced = await this.syncFinancialData(
          ccPatientData,
          apContact.id,
          requestId
        )
      }

      const result: ContactSyncResult = {
        success: true,
        action,
        contact: {
          id: patientRecord.id,
          apId: apContact.id,
          ccId: ccPatientData.id
        },
        apContact,
        customFieldsSynced,
        financialDataSynced,
        message: `Contact ${action} successfully`
      }

      logger.info('Contact sync completed', {
        requestId,
        result: {
          action,
          apId: apContact.id,
          ccId: ccPatientData.id,
          customFieldsSynced,
          financialDataSynced
        }
      })

      return result

    } catch (error) {
      await ErrorLogger.logError(
        'ContactSync',
        error,
        {
          ccPatientId: ccPatientData.id,
          email: ccPatientData.email,
          phone: ccPatientData.phoneMobile,
          requestId,
          options
        },
        'ContactSyncService'
      )

      return {
        success: false,
        action: 'error',
        contact: {
          id: '',
          apId: '',
          ccId: ccPatientData.id
        },
        message: error instanceof Error ? error.message : 'Unknown error during contact sync'
      }
    }
  }

  /**
   * Transform CC patient data to AP contact format
   */
  private transformCCPatientToAPContact(
    ccPatientData: GetCCPatientType,
    isUpdate = false
  ): PostAPContactType {
    const payload: PostAPContactType = {
      email: ccPatientData.email,
      phone: ccPatientData.phoneMobile,
      firstName: ccPatientData.firstName,
      lastName: ccPatientData.lastName,
      dateOfBirth: ccPatientData.dob,
      tags: ['cc_api']
    }

    // Add creation-specific fields
    if (!isUpdate) {
      payload.source = 'cc'
      payload.gender = ccPatientData.gender
    }

    return payload
  }

  /**
   * Create new contact in AP
   */
  private async createNewContact(
    ccPatientData: GetCCPatientType,
    requestId?: string
  ): Promise<{ apContact: GetAPContactType; action: 'created' }> {
    const payload = this.transformCCPatientToAPContact(ccPatientData, false)
    const locationId = getConfig('locationID') as string

    logger.info('Creating new AP contact', {
      requestId,
      ccPatientId: ccPatientData.id,
      email: payload.email,
      phone: payload.phone
    })

    const apContact = await apContactRequest.upsert(payload, locationId)

    if (!apContact || !apContact.id) {
      throw new Error('Failed to create contact in AP - no contact ID returned')
    }

    logger.info('New AP contact created', {
      requestId,
      apId: apContact.id,
      ccPatientId: ccPatientData.id
    })

    return {
      apContact,
      action: 'created'
    }
  }

  /**
   * Update existing contact in AP
   */
  private async updateExistingContact(
    ccPatientData: GetCCPatientType,
    apId: string,
    requestId?: string
  ): Promise<{ apContact: GetAPContactType; action: 'updated' | 'skipped' }> {
    logger.info('Updating existing AP contact', {
      requestId,
      apId,
      ccPatientId: ccPatientData.id
    })

    // Get current AP contact data to check for changes
    const currentApContact = await apContactRequest.get(apId)
    
    if (!currentApContact) {
      throw new Error(`AP contact not found: ${apId}`)
    }

    // Check if update is needed
    const needsUpdate = this.contactNeedsUpdate(ccPatientData, currentApContact)
    
    if (!needsUpdate) {
      logger.info('AP contact is up to date, skipping update', {
        requestId,
        apId,
        ccPatientId: ccPatientData.id
      })

      return {
        apContact: currentApContact,
        action: 'skipped'
      }
    }

    // Perform update
    const updatePayload = this.transformCCPatientToAPContact(ccPatientData, true)
    
    // Preserve existing tags
    if (currentApContact.tags) {
      updatePayload.tags = [...new Set([...(updatePayload.tags || []), ...currentApContact.tags])]
    }

    const updatedContact = await apContactRequest.update(apId, updatePayload)

    if (!updatedContact) {
      throw new Error('Failed to update contact in AP - no contact data returned')
    }

    logger.info('AP contact updated', {
      requestId,
      apId,
      ccPatientId: ccPatientData.id
    })

    return {
      apContact: updatedContact,
      action: 'updated'
    }
  }

  /**
   * Check if AP contact needs updating based on CC data
   */
  private contactNeedsUpdate(
    ccPatientData: GetCCPatientType,
    apContact: GetAPContactType
  ): boolean {
    // Compare key fields to determine if update is needed
    return (
      apContact.firstName !== ccPatientData.firstName ||
      apContact.lastName !== ccPatientData.lastName ||
      apContact.email !== ccPatientData.email ||
      apContact.phone !== ccPatientData.phoneMobile ||
      apContact.dateOfBirth !== ccPatientData.dob
    )
  }

  /**
   * Sync custom fields from CC to AP (placeholder for Sprint 4)
   */
  private async syncCustomFields(
    ccPatientData: GetCCPatientType,
    apId: string,
    requestId?: string
  ): Promise<boolean> {
    try {
      logger.info('Custom fields sync requested', {
        requestId,
        apId,
        ccPatientId: ccPatientData.id,
        customFields: ccPatientData.customFields
      })

      // TODO: Implement in Sprint 4
      // This will call syncCCtoAPCustomfields helper
      
      return true
    } catch (error) {
      logger.error('Custom fields sync failed', {
        requestId,
        apId,
        error: error instanceof Error ? error.message : String(error)
      })
      return false
    }
  }

  /**
   * Sync financial data (invoices/payments) from CC to AP (placeholder for Sprint 4)
   */
  private async syncFinancialData(
    ccPatientData: GetCCPatientType,
    apId: string,
    requestId?: string
  ): Promise<boolean> {
    try {
      logger.info('Financial data sync requested', {
        requestId,
        apId,
        ccPatientId: ccPatientData.id,
        invoices: ccPatientData.invoices?.length || 0,
        payments: ccPatientData.payments?.length || 0
      })

      // TODO: Implement in Sprint 4
      // This will call syncInvoicePayments helper
      
      return true
    } catch (error) {
      logger.error('Financial data sync failed', {
        requestId,
        apId,
        error: error instanceof Error ? error.message : String(error)
      })
      return false
    }
  }
}

// Export singleton instance
export const contactSyncService = new ContactSyncService()
```

### 3. Create Contact Sync Helper Function
Create `src/helpers/contactSync.ts`:

```typescript
import { contactSyncService } from '@/services/contactSyncService'
import type { GetCCPatientType } from '@libCC/CCTypes'
import type { ContactSyncOptions, ContactSyncResult } from '@/types/contactSync'

/**
 * Legacy-compatible updateOrCreateContact function
 * This maintains the same interface as the old helper for easy migration
 */
export async function updateOrCreateContact(
  ccPatientData: GetCCPatientType,
  syncCustomFields = true,
  requestId?: string
): Promise<ContactSyncResult> {
  const options: ContactSyncOptions = {
    syncCustomFields,
    syncFinancialData: true
  }

  return contactSyncService.updateOrCreateContact(
    ccPatientData,
    options,
    requestId
  )
}

/**
 * Enhanced version with more options
 */
export async function syncContact(
  ccPatientData: GetCCPatientType,
  options: ContactSyncOptions = {},
  requestId?: string
): Promise<ContactSyncResult> {
  return contactSyncService.updateOrCreateContact(
    ccPatientData,
    options,
    requestId
  )
}
```

### 4. Update ProcessPatientCreate Job
Update `src/jobs/processPatientCreate.ts` to use the new helper:

```typescript
import { updateOrCreateContact } from '@/helpers/contactSync'

// Replace the manual AP contact creation logic with:
const syncResult = await updateOrCreateContact(
  ccPatientData,
  true, // sync custom fields
  requestId
)

if (!syncResult.success) {
  throw new Error(`Contact sync failed: ${syncResult.message}`)
}

return {
  success: true,
  message: 'Patient created successfully',
  data: {
    patientId: syncResult.contact.id,
    apId: syncResult.contact.apId,
    ccId: syncResult.contact.ccId,
    action: syncResult.action
  }
}
```

## Dependencies
- **Prerequisite**: Tasks 1-2 (Patient job processors)
- **Integrates with**: Patient service, AP contact request layer
- **Blocks**: Task 4 (searchPatient helper)

## Testing Strategy
```typescript
// Test file: src/services/__tests__/contactSyncService.test.ts
describe('ContactSyncService', () => {
  test('should create new contact when none exists', async () => {
    const ccPatientData = createTestPatientData()
    const result = await contactSyncService.updateOrCreateContact(ccPatientData)
    
    expect(result.success).toBe(true)
    expect(result.action).toBe('created')
    expect(result.contact.apId).toBeDefined()
  })
  
  test('should update existing contact with changes', async () => {
    // Setup existing contact
    // Update with new data
    // Verify update occurred
  })
  
  test('should skip update when no changes needed', async () => {
    // Setup existing contact
    // Try to update with same data
    // Verify update was skipped
  })
})
```

## Implementation Notes
- Port the exact logic from legacy updateOrCreateContact function
- Maintain backward compatibility with existing function signature
- Implement proper data transformation between CC and AP formats
- Add placeholders for custom fields and financial data sync (Sprint 4)
- Use comprehensive error handling and logging
- Follow existing project patterns for service organization
