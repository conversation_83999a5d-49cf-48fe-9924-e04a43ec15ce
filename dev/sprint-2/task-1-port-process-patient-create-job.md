# Task 1: Port ProcessPatientCreate Job

## Description
Implement patient creation job processor with contact search/create logic and AP sync. This ports the legacy `ProcessPatientCreate` job from the Bull queue system to the new webhook-based architecture.

## Acceptance Criteria
- [ ] Patient creation job processor implemented using BaseJobProcessor
- [ ] Contact search/create logic ported from legacy system
- [ ] Integration with existing patients table for duplicate prevention
- [ ] AP contact creation and sync functionality
- [ ] Never use `any` or `as any` - use proper TypeScript types
- [ ] Comprehensive error handling and logging

## Technical Implementation Details

### 1. Create Patient Job Types
Create `src/types/patientJobs.ts`:

```typescript
import type { GetCCPatientType } from '@libCC/CCTypes'

export interface ProcessPatientCreatePayload {
  id: number
  firstName: string
  lastName: string
  email?: string
  phoneMobile?: string
  dob?: string
  gender?: string
  customFields?: number[]
  appointments?: number[]
  invoices?: number[]
  payments?: number[]
  updatedAt: string
  createdAt: string
}

export interface PatientJobContext {
  ccPatientData: GetCCPatientType
  location: string
  requestId: string
  skipDuplicateCheck?: boolean
}

export interface ContactSearchResult {
  found: boolean
  contact?: {
    id: string
    apId?: string
    ccId?: number
    email?: string
    phone?: string
  }
  source: 'database' | 'ap_api' | 'new'
}
```

### 2. Create Patient Service
Create `src/services/patientService.ts`:

```typescript
import { getDb } from '@database'
import { patient } from '@database/schema'
import { eq, or } from 'drizzle-orm'
import { logger, ErrorLogger } from '@utils'
import type { GetCCPatientType } from '@libCC/CCTypes'
import type { ContactSearchResult } from '@/types/patientJobs'

export class PatientService {
  private db = getDb()

  /**
   * Search for existing contact by CC ID, email, or phone
   */
  async searchContact(
    ccId: number,
    email?: string,
    phone?: string,
    requestId?: string
  ): Promise<ContactSearchResult> {
    try {
      // First check by CC ID
      if (ccId) {
        const existingByCcId = await this.db
          .select()
          .from(patient)
          .where(eq(patient.ccId, ccId))
          .limit(1)

        if (existingByCcId.length > 0) {
          const contact = existingByCcId[0]
          logger.info('Contact found by CC ID', {
            requestId,
            ccId,
            contactId: contact.id,
            apId: contact.apId
          })

          return {
            found: true,
            contact: {
              id: contact.id,
              apId: contact.apId,
              ccId: contact.ccId,
              email: contact.email || undefined,
              phone: contact.phone || undefined
            },
            source: 'database'
          }
        }
      }

      // Search by email or phone if CC ID not found
      if (email || phone) {
        const conditions = []
        if (email) conditions.push(eq(patient.email, email))
        if (phone) conditions.push(eq(patient.phone, phone))

        const existingByContact = await this.db
          .select()
          .from(patient)
          .where(or(...conditions))
          .limit(1)

        if (existingByContact.length > 0) {
          const contact = existingByContact[0]
          logger.info('Contact found by email/phone', {
            requestId,
            email,
            phone,
            contactId: contact.id,
            apId: contact.apId
          })

          return {
            found: true,
            contact: {
              id: contact.id,
              apId: contact.apId,
              ccId: contact.ccId,
              email: contact.email || undefined,
              phone: contact.phone || undefined
            },
            source: 'database'
          }
        }
      }

      logger.info('No existing contact found', {
        requestId,
        ccId,
        email,
        phone
      })

      return {
        found: false,
        source: 'new'
      }

    } catch (error) {
      await ErrorLogger.logError(
        'ContactSearch',
        error,
        { ccId, email, phone, requestId },
        'PatientService'
      )

      // Return not found on error to allow creation
      return {
        found: false,
        source: 'new'
      }
    }
  }

  /**
   * Create or update patient record in database
   */
  async createOrUpdatePatient(
    ccPatientData: GetCCPatientType,
    apId?: string,
    requestId?: string
  ): Promise<{ id: string; apId: string; ccId: number }> {
    try {
      const existingContact = await this.searchContact(
        ccPatientData.id,
        ccPatientData.email,
        ccPatientData.phoneMobile,
        requestId
      )

      if (existingContact.found && existingContact.contact) {
        // Update existing patient
        const updated = await this.db
          .update(patient)
          .set({
            apId: apId || existingContact.contact.apId,
            email: ccPatientData.email,
            phone: ccPatientData.phoneMobile,
            ccUpdatedAt: new Date(ccPatientData.updatedAt),
            ccData: ccPatientData,
            updatedAt: new Date()
          })
          .where(eq(patient.id, existingContact.contact.id))
          .returning()

        logger.info('Patient record updated', {
          requestId,
          patientId: updated[0].id,
          ccId: ccPatientData.id,
          apId: apId || existingContact.contact.apId
        })

        return {
          id: updated[0].id,
          apId: (apId || existingContact.contact.apId) as string,
          ccId: ccPatientData.id
        }
      } else {
        // Create new patient
        if (!apId) {
          throw new Error('AP ID is required for new patient creation')
        }

        const created = await this.db
          .insert(patient)
          .values({
            apId,
            ccId: ccPatientData.id,
            email: ccPatientData.email,
            phone: ccPatientData.phoneMobile,
            apUpdatedAt: new Date(),
            ccUpdatedAt: new Date(ccPatientData.updatedAt),
            ccData: ccPatientData
          })
          .returning()

        logger.info('New patient record created', {
          requestId,
          patientId: created[0].id,
          ccId: ccPatientData.id,
          apId
        })

        return {
          id: created[0].id,
          apId,
          ccId: ccPatientData.id
        }
      }

    } catch (error) {
      await ErrorLogger.logError(
        'PatientCreateOrUpdate',
        error,
        { ccId: ccPatientData.id, apId, requestId },
        'PatientService'
      )
      throw error
    }
  }
}

// Export singleton instance
export const patientService = new PatientService()
```

### 3. Create ProcessPatientCreate Job
Create `src/jobs/processPatientCreate.ts`:

```typescript
import { BaseJobProcessor } from './baseJobProcessor'
import { patientService } from '@/services/patientService'
import { apContactRequest } from '@libAP'
import { skipLogic } from '@/services/skipLogic'
import { logger, getConfig } from '@utils'
import type { JobPayload, JobResult } from '@/types/jobs'
import type { ProcessPatientCreatePayload } from '@/types/patientJobs'
import type { PostAPContactType } from '@libAP/APTypes'

export class ProcessPatientCreateJob extends BaseJobProcessor<ProcessPatientCreatePayload> {
  protected jobName = 'ProcessPatientCreate'
  protected entityType = 'patient' as const
  protected operation = 'create' as const

  protected async executeJob(
    jobPayload: JobPayload<ProcessPatientCreatePayload>
  ): Promise<JobResult> {
    const { payload, requestId, location } = jobPayload
    const ccPatientData = payload

    logger.info('Processing patient creation', {
      requestId,
      ccPatientId: ccPatientData.id,
      email: ccPatientData.email,
      phone: ccPatientData.phoneMobile
    })

    // Validate required data
    if (!ccPatientData.email && !ccPatientData.phoneMobile) {
      throw new Error('Invalid contact data: email and phone are both missing')
    }

    // Search for existing contact
    const existingContact = await patientService.searchContact(
      ccPatientData.id,
      ccPatientData.email,
      ccPatientData.phoneMobile,
      requestId
    )

    // If contact exists and has AP ID, check skip logic
    if (existingContact.found && existingContact.contact?.apId) {
      const shouldSkip = await skipLogic.hasProcessPatientCreate(
        existingContact.contact.apId,
        location
      )

      if (shouldSkip) {
        return {
          success: true,
          message: `Patient creation skipped - processed recently. AP ID: ${existingContact.contact.apId}`,
          skipped: true,
          data: {
            apId: existingContact.contact.apId,
            ccId: ccPatientData.id
          }
        }
      }

      logger.info('Patient already exists in AP', {
        requestId,
        apId: existingContact.contact.apId,
        ccId: ccPatientData.id
      })

      return {
        success: true,
        message: 'Patient already exists in AP',
        data: {
          apId: existingContact.contact.apId,
          ccId: ccPatientData.id,
          action: 'existing'
        }
      }
    }

    // Create contact in AP
    const apContactPayload: PostAPContactType = {
      email: ccPatientData.email,
      phone: ccPatientData.phoneMobile,
      firstName: ccPatientData.firstName,
      lastName: ccPatientData.lastName,
      tags: ['cc_api'],
      dateOfBirth: ccPatientData.dob,
      source: 'cc',
      gender: ccPatientData.gender
    }

    const locationId = getConfig('locationID') as string
    const apContact = await apContactRequest.upsert(apContactPayload, locationId)

    if (!apContact || !apContact.id) {
      throw new Error('Failed to create contact in AP - no contact ID returned')
    }

    // Save patient record in database
    const patientRecord = await patientService.createOrUpdatePatient(
      ccPatientData,
      apContact.id,
      requestId
    )

    logger.info('Patient creation completed', {
      requestId,
      patientId: patientRecord.id,
      apId: apContact.id,
      ccId: ccPatientData.id
    })

    return {
      success: true,
      message: 'Patient created successfully in AP',
      data: {
        patientId: patientRecord.id,
        apId: apContact.id,
        ccId: ccPatientData.id,
        action: 'created'
      }
    }
  }

  protected isRetryableError(error: unknown): boolean {
    if (error instanceof Error) {
      // Retry on network errors or AP API errors
      return error.message.includes('fetch') ||
             error.message.includes('timeout') ||
             error.message.includes('AP API') ||
             error.message.includes('rate limit')
    }
    return false
  }
}

// Export job processor function for use in job factory
export const processPatientCreate = async (
  jobPayload: JobPayload<ProcessPatientCreatePayload>
): Promise<JobResult> => {
  const processor = new ProcessPatientCreateJob()
  return processor.process(jobPayload)
}
```

### 4. Update Job Factory
Update `src/jobs/jobFactory.ts`:

```typescript
import { processPatientCreate } from './processPatientCreate'
import type { JobPayload, JobResult } from '@/types/jobs'
import type { ProcessPatientCreatePayload } from '@/types/patientJobs'

export class JobFactory {
  async processPatientCreate(
    jobPayload: JobPayload<ProcessPatientCreatePayload>
  ): Promise<JobResult> {
    return processPatientCreate(jobPayload)
  }

  // ... other job methods remain as placeholders
}
```

### 5. Update Event Router
Update `src/webhooks/eventRouter.ts` to use proper typing:

```typescript
import type { ProcessPatientCreatePayload } from '@/types/patientJobs'

private async handleEntityCreated(
  event: CCWebhookEvent,
  location: string,
  requestId: string
): Promise<EventProcessingResult> {
  switch (event.model) {
    case 'Patient':
      return jobFactory.processPatientCreate({
        payload: event.payload as ProcessPatientCreatePayload,
        location,
        requestId,
        eventId: event.id
      })
    
    // ... other cases
  }
}
```

## Dependencies
- **Prerequisite**: Sprint 1 (Webhook Infrastructure)
- **Integrates with**: Existing AP contact request layer, patients database table
- **Blocks**: Task 2 (ProcessPatientUpdate)

## Testing Strategy
```typescript
// Test file: src/jobs/__tests__/processPatientCreate.test.ts
describe('ProcessPatientCreate', () => {
  test('should create new patient in AP', async () => {
    const payload: ProcessPatientCreatePayload = {
      id: 123,
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phoneMobile: '+**********',
      updatedAt: '2023-01-01T00:00:00Z',
      createdAt: '2023-01-01T00:00:00Z'
    }
    
    const result = await processPatientCreate({
      payload,
      location: 'test-clinic',
      requestId: 'test-request',
      eventId: 123
    })
    
    expect(result.success).toBe(true)
    expect(result.data?.apId).toBeDefined()
  })
})
```

## Implementation Notes
- Follow the exact logic from legacy ProcessPatientCreate job
- Use existing AP contact request layer for API calls
- Integrate with patients table for duplicate prevention
- Implement proper error handling for AP API failures
- Add comprehensive logging for debugging
- Use skip logic to prevent duplicate processing
