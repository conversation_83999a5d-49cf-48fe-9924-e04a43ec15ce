# Task 2: Port ProcessPatientUpdate Job

## Description
Implement patient update job processor with existing contact updates and AP sync. This ports the legacy `ProcessPatientUpdate` job to handle patient data changes and sync them to AP.

## Acceptance Criteria
- [ ] Patient update job processor implemented using BaseJobProcessor
- [ ] Existing contact update logic with data validation
- [ ] Custom fields sync integration
- [ ] Skip logic for preventing update loops
- [ ] Never use `any` or `as any` - use proper TypeScript types
- [ ] Comprehensive error handling and logging

## Technical Implementation Details

### 1. Create Patient Update Types
Create `src/types/patientUpdateJobs.ts`:

```typescript
import type { GetCCPatientType } from '@libCC/CCTypes'

export interface ProcessPatientUpdatePayload extends GetCCPatientType {
  // Inherits all fields from GetCCPatientType
  // Additional fields specific to updates can be added here
}

export interface PatientUpdateContext {
  existingPatient?: {
    id: string
    apId: string
    ccId: number
    lastUpdated: string
  }
  hasChanges: boolean
  changedFields: string[]
  requiresCustomFieldSync: boolean
}

export interface PatientUpdateResult {
  updated: boolean
  apContactUpdated: boolean
  customFieldsSynced: boolean
  changes: string[]
}
```

### 2. Extend Patient Service
Update `src/services/patientService.ts`:

```typescript
// Add these methods to the existing PatientService class

/**
 * Check if patient data has meaningful changes
 */
async hasSignificantChanges(
  ccPatientData: GetCCPatientType,
  existingPatient: any,
  requestId?: string
): Promise<{ hasChanges: boolean; changedFields: string[] }> {
  const changedFields: string[] = []
  
  // Check basic contact information
  if (existingPatient.ccData?.firstName !== ccPatientData.firstName) {
    changedFields.push('firstName')
  }
  if (existingPatient.ccData?.lastName !== ccPatientData.lastName) {
    changedFields.push('lastName')
  }
  if (existingPatient.email !== ccPatientData.email) {
    changedFields.push('email')
  }
  if (existingPatient.phone !== ccPatientData.phoneMobile) {
    changedFields.push('phone')
  }
  if (existingPatient.ccData?.dob !== ccPatientData.dob) {
    changedFields.push('dateOfBirth')
  }
  if (existingPatient.ccData?.gender !== ccPatientData.gender) {
    changedFields.push('gender')
  }

  // Check if custom fields have changed
  const existingCustomFields = existingPatient.ccData?.customFields || []
  const newCustomFields = ccPatientData.customFields || []
  
  if (JSON.stringify(existingCustomFields.sort()) !== JSON.stringify(newCustomFields.sort())) {
    changedFields.push('customFields')
  }

  // Check update timestamp
  const existingUpdatedAt = new Date(existingPatient.ccData?.updatedAt || 0)
  const newUpdatedAt = new Date(ccPatientData.updatedAt)
  
  if (newUpdatedAt > existingUpdatedAt) {
    changedFields.push('updatedAt')
  }

  logger.debug('Patient change analysis', {
    requestId,
    ccId: ccPatientData.id,
    changedFields,
    hasChanges: changedFields.length > 0
  })

  return {
    hasChanges: changedFields.length > 0,
    changedFields
  }
}

/**
 * Update existing patient with new CC data
 */
async updatePatientData(
  ccPatientData: GetCCPatientType,
  existingPatientId: string,
  requestId?: string
): Promise<{ id: string; apId: string; ccId: number }> {
  try {
    const updated = await this.db
      .update(patient)
      .set({
        email: ccPatientData.email,
        phone: ccPatientData.phoneMobile,
        ccUpdatedAt: new Date(ccPatientData.updatedAt),
        ccData: ccPatientData,
        updatedAt: new Date()
      })
      .where(eq(patient.id, existingPatientId))
      .returning()

    if (updated.length === 0) {
      throw new Error(`Failed to update patient record: ${existingPatientId}`)
    }

    logger.info('Patient data updated in database', {
      requestId,
      patientId: existingPatientId,
      ccId: ccPatientData.id,
      apId: updated[0].apId
    })

    return {
      id: updated[0].id,
      apId: updated[0].apId,
      ccId: updated[0].ccId
    }

  } catch (error) {
    await ErrorLogger.logError(
      'PatientDataUpdate',
      error,
      { ccId: ccPatientData.id, patientId: existingPatientId, requestId },
      'PatientService'
    )
    throw error
  }
}
```

### 3. Create ProcessPatientUpdate Job
Create `src/jobs/processPatientUpdate.ts`:

```typescript
import { BaseJobProcessor } from './baseJobProcessor'
import { patientService } from '@/services/patientService'
import { apContactRequest } from '@libAP'
import { logger } from '@utils'
import type { JobPayload, JobResult } from '@/types/jobs'
import type { ProcessPatientUpdatePayload, PatientUpdateResult } from '@/types/patientUpdateJobs'
import type { PostAPContactType } from '@libAP/APTypes'

export class ProcessPatientUpdateJob extends BaseJobProcessor<ProcessPatientUpdatePayload> {
  protected jobName = 'ProcessPatientUpdate'
  protected entityType = 'patient' as const
  protected operation = 'update' as const

  protected async executeJob(
    jobPayload: JobPayload<ProcessPatientUpdatePayload>
  ): Promise<JobResult> {
    const { payload, requestId, location } = jobPayload
    const ccPatientData = payload

    logger.info('Processing patient update', {
      requestId,
      ccPatientId: ccPatientData.id,
      updatedAt: ccPatientData.updatedAt
    })

    // Find existing patient
    const existingContact = await patientService.searchContact(
      ccPatientData.id,
      ccPatientData.email,
      ccPatientData.phoneMobile,
      requestId
    )

    if (!existingContact.found || !existingContact.contact) {
      logger.warn('Patient not found for update, creating new patient', {
        requestId,
        ccPatientId: ccPatientData.id
      })

      // If patient doesn't exist, treat as creation
      // This could happen if the create event was missed
      const { processPatientCreate } = await import('./processPatientCreate')
      return processPatientCreate(jobPayload)
    }

    const existingPatient = existingContact.contact

    if (!existingPatient.apId) {
      throw new Error(`Patient exists but has no AP ID: ${existingPatient.id}`)
    }

    // Check for significant changes
    const changeAnalysis = await patientService.hasSignificantChanges(
      ccPatientData,
      existingPatient,
      requestId
    )

    if (!changeAnalysis.hasChanges) {
      logger.info('No significant changes detected, skipping update', {
        requestId,
        ccPatientId: ccPatientData.id,
        apId: existingPatient.apId
      })

      return {
        success: true,
        message: 'No significant changes detected',
        data: {
          apId: existingPatient.apId,
          ccId: ccPatientData.id,
          action: 'skipped',
          reason: 'no_changes'
        }
      }
    }

    logger.info('Significant changes detected', {
      requestId,
      ccPatientId: ccPatientData.id,
      changedFields: changeAnalysis.changedFields
    })

    // Update patient data in database
    const updatedPatient = await patientService.updatePatientData(
      ccPatientData,
      existingPatient.id,
      requestId
    )

    // Update contact in AP
    const updateResult = await this.updateAPContact(
      ccPatientData,
      existingPatient.apId,
      changeAnalysis.changedFields,
      requestId
    )

    // Sync custom fields if needed
    let customFieldsSynced = false
    if (changeAnalysis.changedFields.includes('customFields')) {
      customFieldsSynced = await this.syncCustomFields(
        ccPatientData,
        existingPatient.apId,
        requestId
      )
    }

    const result: PatientUpdateResult = {
      updated: true,
      apContactUpdated: updateResult.success,
      customFieldsSynced,
      changes: changeAnalysis.changedFields
    }

    logger.info('Patient update completed', {
      requestId,
      ccPatientId: ccPatientData.id,
      apId: existingPatient.apId,
      result
    })

    return {
      success: true,
      message: 'Patient updated successfully',
      data: {
        patientId: updatedPatient.id,
        apId: existingPatient.apId,
        ccId: ccPatientData.id,
        action: 'updated',
        changes: changeAnalysis.changedFields,
        result
      }
    }
  }

  private async updateAPContact(
    ccPatientData: ProcessPatientUpdatePayload,
    apId: string,
    changedFields: string[],
    requestId: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Only update fields that have changed
      const updatePayload: Partial<PostAPContactType> = {}

      if (changedFields.includes('firstName')) {
        updatePayload.firstName = ccPatientData.firstName
      }
      if (changedFields.includes('lastName')) {
        updatePayload.lastName = ccPatientData.lastName
      }
      if (changedFields.includes('email')) {
        updatePayload.email = ccPatientData.email
      }
      if (changedFields.includes('phone')) {
        updatePayload.phone = ccPatientData.phoneMobile
      }
      if (changedFields.includes('dateOfBirth')) {
        updatePayload.dateOfBirth = ccPatientData.dob
      }
      if (changedFields.includes('gender')) {
        updatePayload.gender = ccPatientData.gender
      }

      // Only make API call if there are fields to update
      if (Object.keys(updatePayload).length === 0) {
        return {
          success: true,
          message: 'No AP contact fields to update'
        }
      }

      const updatedContact = await apContactRequest.update(apId, updatePayload as PostAPContactType)

      if (!updatedContact) {
        throw new Error('AP contact update returned no data')
      }

      logger.info('AP contact updated successfully', {
        requestId,
        apId,
        updatedFields: Object.keys(updatePayload)
      })

      return {
        success: true,
        message: 'AP contact updated successfully'
      }

    } catch (error) {
      logger.error('Failed to update AP contact', {
        requestId,
        apId,
        error: error instanceof Error ? error.message : String(error)
      })

      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error updating AP contact'
      }
    }
  }

  private async syncCustomFields(
    ccPatientData: ProcessPatientUpdatePayload,
    apId: string,
    requestId: string
  ): Promise<boolean> {
    try {
      // Custom field sync will be implemented in Sprint 4
      // For now, just log that it's needed
      logger.info('Custom field sync needed', {
        requestId,
        apId,
        ccId: ccPatientData.id,
        customFields: ccPatientData.customFields
      })

      return true
    } catch (error) {
      logger.error('Custom field sync failed', {
        requestId,
        apId,
        error: error instanceof Error ? error.message : String(error)
      })

      return false
    }
  }

  protected isRetryableError(error: unknown): boolean {
    if (error instanceof Error) {
      // Retry on network errors or AP API errors
      return error.message.includes('fetch') ||
             error.message.includes('timeout') ||
             error.message.includes('AP API') ||
             error.message.includes('rate limit')
    }
    return false
  }
}

// Export job processor function for use in job factory
export const processPatientUpdate = async (
  jobPayload: JobPayload<ProcessPatientUpdatePayload>
): Promise<JobResult> => {
  const processor = new ProcessPatientUpdateJob()
  return processor.process(jobPayload)
}
```

### 4. Update Job Factory
Update `src/jobs/jobFactory.ts`:

```typescript
import { processPatientUpdate } from './processPatientUpdate'
import type { ProcessPatientUpdatePayload } from '@/types/patientUpdateJobs'

export class JobFactory {
  // ... existing methods

  async processPatientUpdate(
    jobPayload: JobPayload<ProcessPatientUpdatePayload>
  ): Promise<JobResult> {
    return processPatientUpdate(jobPayload)
  }
}
```

### 5. Update Event Router
Update `src/webhooks/eventRouter.ts`:

```typescript
import type { ProcessPatientUpdatePayload } from '@/types/patientUpdateJobs'

private async handleEntityUpdated(
  event: CCWebhookEvent,
  location: string,
  requestId: string
): Promise<EventProcessingResult> {
  switch (event.model) {
    case 'Patient':
      return jobFactory.processPatientUpdate({
        payload: event.payload as ProcessPatientUpdatePayload,
        location,
        requestId,
        eventId: event.id
      })
    
    // ... other cases
  }
}
```

## Dependencies
- **Prerequisite**: Task 1 (ProcessPatientCreate)
- **Integrates with**: Patient service, AP contact request layer
- **Blocks**: Task 3 (updateOrCreateContact helper)

## Testing Strategy
```typescript
// Test file: src/jobs/__tests__/processPatientUpdate.test.ts
describe('ProcessPatientUpdate', () => {
  test('should update existing patient with changes', async () => {
    // Setup existing patient in database
    // Create payload with changes
    // Verify update is processed correctly
  })
  
  test('should skip update when no changes detected', async () => {
    // Setup existing patient
    // Create payload with same data
    // Verify update is skipped
  })
  
  test('should create patient if not found', async () => {
    // Create update payload for non-existent patient
    // Verify it falls back to creation
  })
})
```

## Implementation Notes
- Implement intelligent change detection to avoid unnecessary updates
- Handle cases where patient doesn't exist (fallback to creation)
- Integrate with custom field sync (placeholder for Sprint 4)
- Use proper error handling for AP API failures
- Add comprehensive logging for debugging
- Follow the exact logic from legacy ProcessPatientUpdate job
