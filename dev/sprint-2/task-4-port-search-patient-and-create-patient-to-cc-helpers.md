# Task 4: Port searchPatient and createPatientToCC Helpers

## Description
Implement patient search and creation functions for AP-to-CC sync. These functions handle the reverse direction sync when AP contacts need to be created or found in the CC system.

## Acceptance Criteria
- [ ] searchPatient function ported from legacy system
- [ ] createPatientToCC function implemented
- [ ] updatePatientToCC function for existing patients
- [ ] Integration with CC patient request layer
- [ ] Never use `any` or `as any` - use proper TypeScript types
- [ ] Comprehensive error handling and logging

## Technical Implementation Details

### 1. Create CC Patient Sync Types
Create `src/types/ccPatientSync.ts`:

```typescript
import type { GetAPContactType, PostAPContactType } from '@libAP/APTypes'
import type { GetCCPatientType, PostCCPatientType } from '@libCC/CCTypes'

export interface CCPatientSearchOptions {
  searchByEmail?: boolean
  searchByPhone?: boolean
  createIfNotFound?: boolean
  updateIfFound?: boolean
}

export interface CCPatientSearchResult {
  found: boolean
  patient?: GetCCPatientType
  source: 'email' | 'phone' | 'not_found'
  created?: boolean
  updated?: boolean
}

export interface APToCCPatientData {
  apContact: GetAPContactType
  location: string
  requestId: string
  options?: CCPatientSearchOptions
}

export interface CCPatientSyncResult {
  success: boolean
  action: 'found' | 'created' | 'updated' | 'error'
  patient?: GetCCPatientType
  patientRecord?: {
    id: string
    apId: string
    ccId: number
  }
  message: string
}
```

### 2. Create CC Patient Sync Service
Create `src/services/ccPatientSyncService.ts`:

```typescript
import { patientService } from './patientService'
import { ccPatientRequest } from '@libCC'
import { logger, ErrorLogger } from '@utils'
import type { GetAPContactType } from '@libAP/APTypes'
import type { GetCCPatientType, PostCCPatientType } from '@libCC/CCTypes'
import type { 
  CCPatientSearchOptions, 
  CCPatientSearchResult, 
  CCPatientSyncResult,
  APToCCPatientData 
} from '@/types/ccPatientSync'

export class CCPatientSyncService {
  /**
   * Search for patient in CC by email or phone - ports legacy searchPatient
   */
  async searchPatient(
    apContact: GetAPContactType,
    options: CCPatientSearchOptions = {},
    requestId?: string
  ): Promise<CCPatientSearchResult> {
    const {
      searchByEmail = true,
      searchByPhone = true,
      createIfNotFound = false,
      updateIfFound = false
    } = options

    try {
      logger.info('Searching for patient in CC', {
        requestId,
        apContactId: apContact.id,
        email: apContact.email,
        phone: apContact.phone,
        options
      })

      let patient: GetCCPatientType | null = null
      let source: 'email' | 'phone' | 'not_found' = 'not_found'

      // Search by email first
      if (searchByEmail && apContact.email) {
        try {
          patient = await ccPatientRequest.searchByEmail(apContact.email)
          if (patient) {
            source = 'email'
            logger.info('Patient found by email in CC', {
              requestId,
              ccPatientId: patient.id,
              email: apContact.email
            })
          }
        } catch (error) {
          logger.warn('Email search failed', {
            requestId,
            email: apContact.email,
            error: error instanceof Error ? error.message : String(error)
          })
        }
      }

      // Search by phone if not found by email
      if (!patient && searchByPhone && apContact.phone) {
        try {
          patient = await ccPatientRequest.searchByPhone(apContact.phone)
          if (patient) {
            source = 'phone'
            logger.info('Patient found by phone in CC', {
              requestId,
              ccPatientId: patient.id,
              phone: apContact.phone
            })
          }
        } catch (error) {
          logger.warn('Phone search failed', {
            requestId,
            phone: apContact.phone,
            error: error instanceof Error ? error.message : String(error)
          })
        }
      }

      if (!patient) {
        if (createIfNotFound) {
          const createResult = await this.createPatientInCC(apContact, requestId)
          return {
            found: true,
            patient: createResult.patient,
            source: 'not_found',
            created: true
          }
        }

        return {
          found: false,
          source: 'not_found'
        }
      }

      // Update existing patient if requested
      let updated = false
      if (updateIfFound) {
        const updateResult = await this.updatePatientInCC(patient.id, apContact, requestId)
        if (updateResult.success) {
          patient = updateResult.patient || patient
          updated = true
        }
      }

      return {
        found: true,
        patient,
        source,
        updated
      }

    } catch (error) {
      await ErrorLogger.logError(
        'CCPatientSearch',
        error,
        {
          apContactId: apContact.id,
          email: apContact.email,
          phone: apContact.phone,
          requestId,
          options
        },
        'CCPatientSyncService'
      )

      return {
        found: false,
        source: 'not_found'
      }
    }
  }

  /**
   * Create patient in CC from AP contact data - ports legacy createPatientToCC
   */
  async createPatientToCC(
    apToCCData: APToCCPatientData
  ): Promise<CCPatientSyncResult> {
    const { apContact, location, requestId, options = {} } = apToCCData

    try {
      logger.info('Creating patient in CC from AP contact', {
        requestId,
        apContactId: apContact.id,
        email: apContact.email,
        phone: apContact.phone
      })

      // Check if patient already exists in our database
      const existingContact = await patientService.searchContact(
        0, // No CC ID yet
        apContact.email,
        apContact.phone,
        requestId
      )

      if (existingContact.found && existingContact.contact?.ccId) {
        logger.info('Patient already exists in CC', {
          requestId,
          ccPatientId: existingContact.contact.ccId,
          apContactId: apContact.id
        })

        // Get the CC patient data
        const ccPatient = await ccPatientRequest.getPatient(existingContact.contact.ccId)
        
        return {
          success: true,
          action: 'found',
          patient: ccPatient,
          patientRecord: {
            id: existingContact.contact.id,
            apId: apContact.id,
            ccId: existingContact.contact.ccId
          },
          message: 'Patient already exists in CC'
        }
      }

      // Search for patient in CC
      const searchResult = await this.searchPatient(apContact, options, requestId)

      if (searchResult.found && searchResult.patient) {
        // Patient found, update our database mapping
        const patientRecord = await patientService.createOrUpdatePatient(
          searchResult.patient,
          apContact.id,
          requestId
        )

        return {
          success: true,
          action: searchResult.created ? 'created' : 'found',
          patient: searchResult.patient,
          patientRecord,
          message: searchResult.created ? 'Patient created in CC' : 'Patient found in CC'
        }
      }

      // Create new patient in CC
      const createResult = await this.createPatientInCC(apContact, requestId)
      
      // Update our database mapping
      const patientRecord = await patientService.createOrUpdatePatient(
        createResult.patient,
        apContact.id,
        requestId
      )

      return {
        success: true,
        action: 'created',
        patient: createResult.patient,
        patientRecord,
        message: 'Patient created in CC successfully'
      }

    } catch (error) {
      await ErrorLogger.logError(
        'CreatePatientToCC',
        error,
        {
          apContactId: apContact.id,
          email: apContact.email,
          phone: apContact.phone,
          location,
          requestId
        },
        'CCPatientSyncService'
      )

      return {
        success: false,
        action: 'error',
        message: error instanceof Error ? error.message : 'Unknown error creating patient in CC'
      }
    }
  }

  /**
   * Update existing patient in CC - ports legacy updatePatientToCC
   */
  async updatePatientToCC(
    ccPatientId: number,
    apContact: GetAPContactType,
    requestId?: string
  ): Promise<CCPatientSyncResult> {
    try {
      logger.info('Updating patient in CC', {
        requestId,
        ccPatientId,
        apContactId: apContact.id
      })

      const updatePayload = this.transformAPContactToCCPatient(apContact)
      
      // Add custom fields sync (placeholder for Sprint 4)
      // updatePayload.customFields = await this.syncAPToCCCustomFields(apContact, requestId)

      const updatedPatient = await ccPatientRequest.updatePatient(ccPatientId, updatePayload)

      logger.info('Patient updated in CC', {
        requestId,
        ccPatientId,
        apContactId: apContact.id
      })

      return {
        success: true,
        action: 'updated',
        patient: updatedPatient,
        message: 'Patient updated in CC successfully'
      }

    } catch (error) {
      await ErrorLogger.logError(
        'UpdatePatientToCC',
        error,
        { ccPatientId, apContactId: apContact.id, requestId },
        'CCPatientSyncService'
      )

      return {
        success: false,
        action: 'error',
        message: error instanceof Error ? error.message : 'Unknown error updating patient in CC'
      }
    }
  }

  /**
   * Create new patient in CC
   */
  private async createPatientInCC(
    apContact: GetAPContactType,
    requestId?: string
  ): Promise<{ patient: GetCCPatientType; success: boolean }> {
    const payload = this.transformAPContactToCCPatient(apContact)
    
    // Add custom fields sync (placeholder for Sprint 4)
    // payload.customFields = await this.syncAPToCCCustomFields(apContact, requestId)

    logger.info('Creating new patient in CC', {
      requestId,
      apContactId: apContact.id,
      payload: {
        firstName: payload.firstName,
        lastName: payload.lastName,
        email: payload.email,
        phone: payload.phoneMobile
      }
    })

    const createdPatient = await ccPatientRequest.createPatient(payload)

    if (!createdPatient || !createdPatient.id) {
      throw new Error('Failed to create patient in CC - no patient ID returned')
    }

    logger.info('Patient created in CC', {
      requestId,
      ccPatientId: createdPatient.id,
      apContactId: apContact.id
    })

    return {
      patient: createdPatient,
      success: true
    }
  }

  /**
   * Transform AP contact data to CC patient format
   */
  private transformAPContactToCCPatient(apContact: GetAPContactType): PostCCPatientType {
    return {
      firstName: apContact.firstName || '',
      lastName: apContact.lastName || '',
      email: apContact.email,
      phoneMobile: apContact.phone,
      dob: apContact.dateOfBirth,
      gender: apContact.gender
      // customFields will be added in Sprint 4
    }
  }

  /**
   * Sync custom fields from AP to CC (placeholder for Sprint 4)
   */
  private async syncAPToCCCustomFields(
    apContact: GetAPContactType,
    requestId?: string
  ): Promise<any[]> {
    logger.info('AP to CC custom fields sync requested', {
      requestId,
      apContactId: apContact.id,
      customFields: apContact.customFields?.length || 0
    })

    // TODO: Implement in Sprint 4
    return []
  }
}

// Export singleton instance
export const ccPatientSyncService = new CCPatientSyncService()
```

### 3. Create Helper Functions
Create `src/helpers/ccPatientSync.ts`:

```typescript
import { ccPatientSyncService } from '@/services/ccPatientSyncService'
import type { GetAPContactType } from '@libAP/APTypes'
import type { CCPatientSearchOptions, CCPatientSyncResult } from '@/types/ccPatientSync'

/**
 * Legacy-compatible searchPatient function
 */
export async function searchPatient(
  apContact: GetAPContactType,
  requestId?: string
): Promise<CCPatientSyncResult> {
  const options: CCPatientSearchOptions = {
    searchByEmail: true,
    searchByPhone: true,
    createIfNotFound: true
  }

  const searchResult = await ccPatientSyncService.searchPatient(apContact, options, requestId)
  
  if (searchResult.found) {
    return {
      success: true,
      action: searchResult.created ? 'created' : 'found',
      patient: searchResult.patient,
      message: searchResult.created ? 'Patient created in CC' : 'Patient found in CC'
    }
  }

  return {
    success: false,
    action: 'error',
    message: 'Patient not found and creation failed'
  }
}

/**
 * Legacy-compatible createPatientToCC function
 */
export async function createPatientToCC(
  apContact: GetAPContactType,
  location: string,
  requestId?: string
): Promise<CCPatientSyncResult> {
  return ccPatientSyncService.createPatientToCC({
    apContact,
    location,
    requestId: requestId || crypto.randomUUID()
  })
}

/**
 * Legacy-compatible updatePatientToCC function
 */
export async function updatePatientToCC(
  ccPatientId: number,
  apContact: GetAPContactType,
  requestId?: string
): Promise<CCPatientSyncResult> {
  return ccPatientSyncService.updatePatientToCC(ccPatientId, apContact, requestId)
}
```

## Dependencies
- **Prerequisite**: Task 3 (updateOrCreateContact helper)
- **Integrates with**: CC patient request layer, patient service
- **Blocks**: Sprint 5 AP webhook handlers

## Testing Strategy
```typescript
// Test file: src/services/__tests__/ccPatientSyncService.test.ts
describe('CCPatientSyncService', () => {
  test('should find patient by email in CC', async () => {
    const apContact = createTestAPContact()
    const result = await ccPatientSyncService.searchPatient(apContact)
    
    expect(result.found).toBe(true)
    expect(result.source).toBe('email')
  })
  
  test('should create patient in CC when not found', async () => {
    const apContact = createTestAPContact()
    const result = await ccPatientSyncService.createPatientToCC({
      apContact,
      location: 'test-clinic',
      requestId: 'test-request'
    })
    
    expect(result.success).toBe(true)
    expect(result.action).toBe('created')
  })
})
```

## Implementation Notes
- Port exact logic from legacy searchPatient and createPatientToCC functions
- Implement proper error handling for CC API failures
- Add placeholders for custom field sync (Sprint 4)
- Use comprehensive logging for debugging
- Follow existing project patterns for service organization
- Maintain backward compatibility with legacy function signatures
