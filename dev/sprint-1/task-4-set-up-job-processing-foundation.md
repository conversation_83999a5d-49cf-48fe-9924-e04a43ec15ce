# Task 4: Create Direct Webhook Handlers

## Description

Create direct webhook handlers to process CliniCore events immediately without job queues. This provides the foundation for processing webhook events synchronously with proper error handling and response management.

## Acceptance Criteria

- [ ] Direct webhook handlers for each entity type
- [ ] Consistent response format across all handlers
- [ ] Error handling and retry logic for failed operations
- [ ] Handler context with request ID and metadata
- [ ] Never use `any` or `as any` - use proper TypeScript types
- [ ] Integration with skip logic and error logging

## Technical Implementation Details

### 1. Create Job Processing Types

Create `src/types/jobs.ts`:

```typescript
export interface JobContext {
  requestId: string;
  location: string;
  eventId: number | string;
  timestamp: string;
  metadata?: Record<string, unknown>;
}

export interface JobPayload<T = Record<string, unknown>> {
  payload: T;
  location: string;
  requestId: string;
  eventId: number | string;
  metadata?: Record<string, unknown>;
}

export interface JobResult {
  success: boolean;
  message: string;
  data?: Record<string, unknown>;
  processingTime?: number;
  skipped?: boolean;
  retryable?: boolean;
}

export interface JobProcessor<TPayload = Record<string, unknown>> {
  process(jobPayload: JobPayload<TPayload>): Promise<JobResult>;
}

export interface JobExecutionOptions {
  maxRetries?: number;
  retryDelay?: number;
  timeout?: number;
}
```

### 2. Create Job Executor Service

Create `src/services/jobExecutor.ts`:

```typescript
import { logger, ErrorLogger } from "@utils";
import { skipLogic } from "@/services/skipLogic";
import type {
  JobPayload,
  JobResult,
  JobProcessor,
  JobExecutionOptions,
} from "@/types/jobs";

export class JobExecutor {
  private defaultOptions: Required<JobExecutionOptions> = {
    maxRetries: 3,
    retryDelay: 1000, // 1 second
    timeout: 30000, // 30 seconds
  };

  async executeJob<TPayload = Record<string, unknown>>(
    processor: JobProcessor<TPayload>,
    jobPayload: JobPayload<TPayload>,
    options: JobExecutionOptions = {}
  ): Promise<JobResult> {
    const opts = { ...this.defaultOptions, ...options };
    const startTime = Date.now();

    logger.info("Job execution started", {
      requestId: jobPayload.requestId,
      location: jobPayload.location,
      eventId: jobPayload.eventId,
    });

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= opts.maxRetries; attempt++) {
      try {
        const result = await this.executeWithTimeout(
          processor,
          jobPayload,
          opts.timeout
        );

        const processingTime = Date.now() - startTime;

        logger.info("Job execution completed", {
          requestId: jobPayload.requestId,
          success: result.success,
          attempt,
          processingTime,
        });

        return {
          ...result,
          processingTime,
        };
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        logger.warn("Job execution attempt failed", {
          requestId: jobPayload.requestId,
          attempt,
          maxRetries: opts.maxRetries,
          error: lastError.message,
        });

        // Don't retry if it's the last attempt
        if (attempt === opts.maxRetries) {
          break;
        }

        // Wait before retry
        await this.delay(opts.retryDelay * attempt); // Exponential backoff
      }
    }

    // All retries failed
    const processingTime = Date.now() - startTime;

    await ErrorLogger.logError(
      "JobExecutionFailed",
      lastError || new Error("Unknown job execution error"),
      {
        requestId: jobPayload.requestId,
        location: jobPayload.location,
        eventId: jobPayload.eventId,
        attempts: opts.maxRetries,
        processingTime,
      },
      "JobExecutor"
    );

    return {
      success: false,
      message: `Job failed after ${opts.maxRetries} attempts: ${
        lastError?.message || "Unknown error"
      }`,
      processingTime,
      retryable: false,
    };
  }

  private async executeWithTimeout<TPayload>(
    processor: JobProcessor<TPayload>,
    jobPayload: JobPayload<TPayload>,
    timeout: number
  ): Promise<JobResult> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Job execution timed out after ${timeout}ms`));
      }, timeout);

      processor
        .process(jobPayload)
        .then((result) => {
          clearTimeout(timeoutId);
          resolve(result);
        })
        .catch((error) => {
          clearTimeout(timeoutId);
          reject(error);
        });
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const jobExecutor = new JobExecutor();
```

### 3. Create Base Job Processor

Create `src/jobs/baseJobProcessor.ts`:

```typescript
import { logger, ErrorLogger } from "@utils";
import { skipLogic } from "@/services/skipLogic";
import type { JobPayload, JobResult, JobProcessor } from "@/types/jobs";

export abstract class BaseJobProcessor<TPayload = Record<string, unknown>>
  implements JobProcessor<TPayload>
{
  protected abstract jobName: string;
  protected abstract entityType:
    | "patient"
    | "appointment"
    | "invoice"
    | "payment";
  protected abstract operation: "create" | "update" | "delete";

  async process(jobPayload: JobPayload<TPayload>): Promise<JobResult> {
    const { requestId, location, eventId } = jobPayload;

    try {
      logger.info(`${this.jobName} started`, {
        requestId,
        location,
        eventId,
      });

      // Check skip logic if applicable
      if (this.shouldCheckSkipLogic()) {
        const skipResult = await skipLogic.shouldSkip(
          this.entityType,
          eventId,
          this.operation,
          location,
          requestId
        );

        if (skipResult.shouldSkip) {
          return {
            success: true,
            message:
              skipResult.reason || "Processing skipped due to recent execution",
            skipped: true,
          };
        }
      }

      // Execute the actual job logic
      const result = await this.executeJob(jobPayload);

      // Record processing in skip logic
      if (this.shouldCheckSkipLogic() && result.success) {
        await skipLogic.recordProcessing(
          this.entityType,
          eventId,
          this.operation,
          location,
          requestId,
          { jobName: this.jobName }
        );
      }

      logger.info(`${this.jobName} completed`, {
        requestId,
        success: result.success,
        message: result.message,
      });

      return result;
    } catch (error) {
      await ErrorLogger.logError(
        this.jobName,
        error,
        {
          requestId,
          location,
          eventId,
          payload: jobPayload.payload,
        },
        this.jobName
      );

      return {
        success: false,
        message:
          error instanceof Error ? error.message : "Unknown error occurred",
        retryable: this.isRetryableError(error),
      };
    }
  }

  protected abstract executeJob(
    jobPayload: JobPayload<TPayload>
  ): Promise<JobResult>;

  protected shouldCheckSkipLogic(): boolean {
    return true; // Override in subclasses if skip logic not needed
  }

  protected isRetryableError(error: unknown): boolean {
    // Override in subclasses for specific retry logic
    if (error instanceof Error) {
      // Network errors are typically retryable
      return (
        error.message.includes("fetch") ||
        error.message.includes("timeout") ||
        error.message.includes("ECONNRESET")
      );
    }
    return false;
  }
}
```

### 4. Create Job Factory

Create `src/jobs/jobFactory.ts`:

```typescript
import type { JobPayload, JobResult } from "@/types/jobs";
import { jobExecutor } from "@/services/jobExecutor";

// Import job processors (will be implemented in subsequent sprints)
// import { ProcessPatientCreateJob } from './processPatientCreate'
// import { ProcessPatientUpdateJob } from './processPatientUpdate'
// ... etc

export class JobFactory {
  async processPatientCreate(jobPayload: JobPayload): Promise<JobResult> {
    // Will be implemented in Sprint 2
    return {
      success: true,
      message: "ProcessPatientCreate - placeholder implementation",
    };
  }

  async processPatientUpdate(jobPayload: JobPayload): Promise<JobResult> {
    // Will be implemented in Sprint 2
    return {
      success: true,
      message: "ProcessPatientUpdate - placeholder implementation",
    };
  }

  async processCcAppointmentCreate(jobPayload: JobPayload): Promise<JobResult> {
    // Will be implemented in Sprint 3
    return {
      success: true,
      message: "ProcessCcAppointmentCreate - placeholder implementation",
    };
  }

  async processCcAppointmentUpdate(jobPayload: JobPayload): Promise<JobResult> {
    // Will be implemented in Sprint 3
    return {
      success: true,
      message: "ProcessCcAppointmentUpdate - placeholder implementation",
    };
  }

  async processCcAppointmentDelete(jobPayload: JobPayload): Promise<JobResult> {
    // Will be implemented in Sprint 3
    return {
      success: true,
      message: "ProcessCcAppointmentDelete - placeholder implementation",
    };
  }

  async processCcInvoice(jobPayload: JobPayload): Promise<JobResult> {
    // Will be implemented in Sprint 4
    return {
      success: true,
      message: "ProcessCcInvoice - placeholder implementation",
    };
  }

  async processCcPayment(jobPayload: JobPayload): Promise<JobResult> {
    // Will be implemented in Sprint 4
    return {
      success: true,
      message: "ProcessCcPayment - placeholder implementation",
    };
  }
}

// Export singleton instance
export const jobFactory = new JobFactory();
```

### 5. Update Event Router to Use Job Factory

Update the imports in `src/webhooks/eventRouter.ts`:

```typescript
// Replace the job imports with:
import { jobFactory } from '@/jobs/jobFactory'

// Update the handler methods to use jobFactory:
private async handleEntityCreated(
  event: CCWebhookEvent,
  location: string,
  requestId: string
): Promise<EventProcessingResult> {
  const jobPayload = {
    payload: event.payload,
    location,
    requestId,
    eventId: event.id
  }

  switch (event.model) {
    case 'Patient':
      return jobFactory.processPatientCreate(jobPayload)

    case 'Invoice':
      return jobFactory.processCcInvoice(jobPayload)

    case 'Payment':
      return jobFactory.processCcPayment(jobPayload)

    default:
      // ... rest of the method
  }
}
```

## Dependencies

- **Prerequisite**: Task 3 (Skip Logic Utilities)
- **Blocks**: All job processor implementations in Sprints 2-4

## Testing Strategy

```typescript
// Test file: src/services/__tests__/jobExecutor.test.ts
describe("JobExecutor", () => {
  test("should execute job successfully", async () => {
    const mockProcessor = {
      process: jest
        .fn()
        .mockResolvedValue({ success: true, message: "Test job completed" }),
    };

    const result = await jobExecutor.executeJob(mockProcessor, {
      payload: { test: "data" },
      location: "test",
      requestId: "test-id",
      eventId: 123,
    });

    expect(result.success).toBe(true);
    expect(mockProcessor.process).toHaveBeenCalled();
  });
});
```

## Implementation Notes

- Provide consistent interface for all job processors
- Implement proper error handling and retry logic
- Integrate with skip logic and error logging systems
- Use timeout protection for long-running jobs
- Follow existing project patterns for service organization
