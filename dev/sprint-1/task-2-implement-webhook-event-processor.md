# Task 2: Implement CliniCore Event Router

## Description

Create central event router that processes CliniCore webhook events directly and routes them to appropriate handlers based on event type and model. This replaces the legacy Socket.io event routing logic with direct webhook processing.

## Acceptance Criteria

- [ ] Event routing logic implemented for all CliniCore webhook events
- [ ] Proper event-to-handler mapping based on legacy Socket.js patterns
- [ ] Direct processing without job queues
- [ ] Error handling for unknown events or models
- [ ] Logging for event processing flow
- [ ] Never use `any` or `as any` - use proper TypeScript types
- [ ] Database timestamp-based skip logic

## Technical Implementation Details

### 1. Create CliniCore Event Router

Create `src/webhooks/ccEventRouter.ts`:

```typescript
import type { CCWebhookEvent } from "@/types/webhooks";
import { logger, ErrorLogger } from "@utils";
import { processPatientCreate } from "@/jobs/processPatientCreate";
import { processPatientUpdate } from "@/jobs/processPatientUpdate";
import { processCcAppointmentCreate } from "@/jobs/processCcAppointmentCreate";
import { processCcAppointmentUpdate } from "@/jobs/processCcAppointmentUpdate";
import { processCcAppointmentDelete } from "@/jobs/processCcAppointmentDelete";
import { processCcInvoice } from "@/jobs/processCcInvoice";
import { processCcPayment } from "@/jobs/processCcPayment";

export interface EventProcessingResult {
  success: boolean;
  message: string;
  data?: Record<string, unknown>;
  processingTime?: number;
}

export class WebhookEventRouter {
  async routeEvent(
    event: CCWebhookEvent,
    location: string,
    requestId: string
  ): Promise<EventProcessingResult> {
    const startTime = Date.now();

    try {
      logger.info("Routing webhook event", {
        requestId,
        event: event.event,
        model: event.model,
        id: event.id,
        location,
      });

      const result = await this.processEvent(event, location, requestId);

      const processingTime = Date.now() - startTime;
      logger.info("Event processed successfully", {
        requestId,
        processingTime,
        event: event.event,
        model: event.model,
      });

      return {
        ...result,
        processingTime,
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;

      await ErrorLogger.logError(
        "WebhookEventProcessing",
        error,
        {
          event: event.event,
          model: event.model,
          id: event.id,
          location,
          requestId,
          processingTime,
        },
        "WebhookEventRouter"
      );

      throw error;
    }
  }

  private async processEvent(
    event: CCWebhookEvent,
    location: string,
    requestId: string
  ): Promise<EventProcessingResult> {
    // Map legacy socket events to new webhook events
    switch (event.event) {
      case "EntityWasCreated":
        return this.handleEntityCreated(event, location, requestId);

      case "EntityWasUpdated":
        return this.handleEntityUpdated(event, location, requestId);

      case "EntityWasDeleted":
        return this.handleEntityDeleted(event, location, requestId);

      case "AppointmentWasCreated":
        return this.handleAppointmentCreated(event, location, requestId);

      default:
        throw new Error(`Unknown event type: ${event.event}`);
    }
  }

  private async handleEntityCreated(
    event: CCWebhookEvent,
    location: string,
    requestId: string
  ): Promise<EventProcessingResult> {
    switch (event.model) {
      case "Patient":
        return processPatientCreate({
          payload: event.payload,
          location,
          requestId,
          eventId: event.id,
        });

      case "Invoice":
        return processCcInvoice({
          payload: event.payload,
          location,
          requestId,
          eventId: event.id,
        });

      case "Payment":
        return processCcPayment({
          payload: event.payload,
          location,
          requestId,
          eventId: event.id,
        });

      default:
        logger.warn("Unhandled EntityWasCreated model", {
          model: event.model,
          requestId,
        });
        return {
          success: true,
          message: `EntityWasCreated for ${event.model} - no handler implemented`,
        };
    }
  }

  private async handleEntityUpdated(
    event: CCWebhookEvent,
    location: string,
    requestId: string
  ): Promise<EventProcessingResult> {
    switch (event.model) {
      case "Patient":
        return processPatientUpdate({
          payload: event.payload,
          location,
          requestId,
          eventId: event.id,
        });

      case "Appointment":
        return processCcAppointmentUpdate({
          payload: event.payload,
          location,
          requestId,
          eventId: event.id,
        });

      default:
        logger.warn("Unhandled EntityWasUpdated model", {
          model: event.model,
          requestId,
        });
        return {
          success: true,
          message: `EntityWasUpdated for ${event.model} - no handler implemented`,
        };
    }
  }

  private async handleEntityDeleted(
    event: CCWebhookEvent,
    location: string,
    requestId: string
  ): Promise<EventProcessingResult> {
    switch (event.model) {
      case "Appointment":
        return processCcAppointmentDelete({
          payload: event.id, // For deletes, we only get the ID
          location,
          requestId,
          eventId: event.id,
        });

      default:
        logger.warn("Unhandled EntityWasDeleted model", {
          model: event.model,
          requestId,
        });
        return {
          success: true,
          message: `EntityWasDeleted for ${event.model} - no handler implemented`,
        };
    }
  }

  private async handleAppointmentCreated(
    event: CCWebhookEvent,
    location: string,
    requestId: string
  ): Promise<EventProcessingResult> {
    return processCcAppointmentCreate({
      payload: event.payload,
      location,
      requestId,
      eventId: event.id,
    });
  }
}

// Export singleton instance
export const webhookEventRouter = new WebhookEventRouter();
```

### 2. Update Webhook Processor

Update `src/webhooks/processor.ts` to use the event router:

```typescript
import type { Context } from "hono";
import type { CCWebhookEvent, WebhookResponse } from "@/types/webhooks";
import { logger } from "@utils";
import { createError } from "@utils";
import { webhookEventRouter } from "./eventRouter";

export const webhookProcessor = async (c: Context): Promise<Response> => {
  const requestId = crypto.randomUUID();
  const location = c.req.param("location");

  try {
    const body = (await c.req.json()) as CCWebhookEvent;

    // Validate required fields
    if (!body.event || !body.model || !body.id) {
      throw createError("Missing required fields: event, model, or id", 400);
    }

    // Process the event through the router
    const result = await webhookEventRouter.routeEvent(
      body,
      location,
      requestId
    );

    const response: WebhookResponse = {
      success: result.success,
      message: result.message,
      requestId,
      processedAt: new Date().toISOString(),
      data: result.data,
    };

    return c.json(response, result.success ? 200 : 500);
  } catch (error) {
    logger.error("Webhook processing failed", {
      requestId,
      location,
      error: error instanceof Error ? error.message : String(error),
    });

    const response: WebhookResponse = {
      success: false,
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
      requestId,
      processedAt: new Date().toISOString(),
    };

    const statusCode =
      error instanceof Error && "status" in error
        ? (error as { status: number }).status
        : 500;
    return c.json(response, statusCode);
  }
};
```

## Dependencies

- **Prerequisite**: Task 1 (Webhook Routing Infrastructure)
- **Blocks**: All Sprint 2 job processor tasks

## Testing Strategy

```typescript
// Test file: src/webhooks/__tests__/eventRouter.test.ts
describe("WebhookEventRouter", () => {
  test("should route patient creation event", async () => {
    const event: CCWebhookEvent = {
      event: "EntityWasCreated",
      model: "Patient",
      id: 123,
      payload: { firstName: "John", lastName: "Doe" },
    };

    const result = await webhookEventRouter.routeEvent(
      event,
      "test-location",
      "test-request-id"
    );
    expect(result.success).toBe(true);
  });
});
```

## Implementation Notes

- Follow the exact event mapping from legacy Socket.io handlers
- Ensure all job processors return consistent result format
- Add comprehensive logging for debugging
- Handle unknown events gracefully with warnings
