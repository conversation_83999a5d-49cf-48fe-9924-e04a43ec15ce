# Task 3: Create Skip Logic Utilities

## Description
Port skip logic from legacy system to prevent duplicate processing using KV storage. This replaces the legacy database-based Skip model with Cloudflare KV storage for better performance.

## Acceptance Criteria
- [ ] Skip logic utilities created using Cloudflare KV storage
- [ ] Duplicate prevention for patient and appointment processing
- [ ] Configurable time windows for skip logic
- [ ] Proper error handling for KV operations
- [ ] Never use `any` or `as any` - use proper TypeScript types
- [ ] Integration with existing job processors

## Technical Implementation Details

### 1. Create Skip Logic Types
Create `src/types/skipLogic.ts`:

```typescript
export interface SkipEntry {
  key: string
  entityType: 'patient' | 'appointment' | 'invoice' | 'payment'
  entityId: string | number
  operation: 'create' | 'update' | 'delete'
  location: string
  createdAt: string
  expiresAt: string
  metadata?: Record<string, unknown>
}

export interface SkipLogicConfig {
  patientCreateWindow: number // seconds
  patientUpdateWindow: number
  appointmentCreateWindow: number
  appointmentUpdateWindow: number
  appointmentDeleteWindow: number
  invoiceProcessWindow: number
  paymentProcessWindow: number
}

export interface SkipCheckResult {
  shouldSkip: boolean
  reason?: string
  existingEntry?: SkipEntry
}
```

### 2. Create Skip Logic Service
Create `src/services/skipLogic.ts`:

```typescript
import { getKV, logger, ErrorLogger } from '@utils'
import type { SkipEntry, SkipLogicConfig, SkipCheckResult } from '@/types/skipLogic'

export class SkipLogicService {
  private kv = getKV()
  
  private config: SkipLogicConfig = {
    patientCreateWindow: 30, // 30 seconds
    patientUpdateWindow: 60, // 1 minute
    appointmentCreateWindow: 30,
    appointmentUpdateWindow: 60,
    appointmentDeleteWindow: 30,
    invoiceProcessWindow: 120, // 2 minutes
    paymentProcessWindow: 120
  }

  /**
   * Check if an operation should be skipped based on recent processing
   */
  async shouldSkip(
    entityType: SkipEntry['entityType'],
    entityId: string | number,
    operation: SkipEntry['operation'],
    location: string,
    requestId: string
  ): Promise<SkipCheckResult> {
    try {
      const key = this.generateSkipKey(entityType, entityId, operation, location)
      const existing = await this.getSkipEntry(key)
      
      if (existing && !this.isExpired(existing)) {
        logger.info('Operation skipped due to recent processing', {
          requestId,
          entityType,
          entityId,
          operation,
          location,
          existingEntry: existing.createdAt
        })
        
        return {
          shouldSkip: true,
          reason: `${operation} for ${entityType} ${entityId} was processed recently`,
          existingEntry: existing
        }
      }
      
      return { shouldSkip: false }
      
    } catch (error) {
      await ErrorLogger.logError(
        'SkipLogicCheck',
        error,
        { entityType, entityId, operation, location, requestId },
        'SkipLogicService'
      )
      
      // On error, don't skip to ensure processing continues
      return { shouldSkip: false, reason: 'Skip check failed, proceeding with processing' }
    }
  }

  /**
   * Record that an operation has been processed
   */
  async recordProcessing(
    entityType: SkipEntry['entityType'],
    entityId: string | number,
    operation: SkipEntry['operation'],
    location: string,
    requestId: string,
    metadata?: Record<string, unknown>
  ): Promise<void> {
    try {
      const key = this.generateSkipKey(entityType, entityId, operation, location)
      const windowSeconds = this.getWindowForOperation(entityType, operation)
      const now = new Date()
      const expiresAt = new Date(now.getTime() + windowSeconds * 1000)
      
      const entry: SkipEntry = {
        key,
        entityType,
        entityId,
        operation,
        location,
        createdAt: now.toISOString(),
        expiresAt: expiresAt.toISOString(),
        metadata: {
          requestId,
          ...metadata
        }
      }
      
      // Store in KV with TTL
      await this.kv.put(key, JSON.stringify(entry), {
        expirationTtl: windowSeconds
      })
      
      logger.debug('Skip entry recorded', {
        requestId,
        key,
        entityType,
        entityId,
        operation,
        expiresAt: entry.expiresAt
      })
      
    } catch (error) {
      await ErrorLogger.logError(
        'SkipLogicRecord',
        error,
        { entityType, entityId, operation, location, requestId },
        'SkipLogicService'
      )
      // Don't throw - recording skip logic failure shouldn't break processing
    }
  }

  /**
   * Check if a patient creation should be skipped (legacy compatibility)
   */
  async hasProcessPatientCreate(apId: string, location: string): Promise<boolean> {
    const result = await this.shouldSkip('patient', apId, 'create', location, 'legacy-check')
    return result.shouldSkip
  }

  /**
   * Check if an appointment operation should be skipped
   */
  async hasProcessAppointment(
    appointmentId: string | number,
    operation: 'create' | 'update' | 'delete',
    location: string
  ): Promise<boolean> {
    const result = await this.shouldSkip('appointment', appointmentId, operation, location, 'legacy-check')
    return result.shouldSkip
  }

  private generateSkipKey(
    entityType: string,
    entityId: string | number,
    operation: string,
    location: string
  ): string {
    return `skip:${location}:${entityType}:${operation}:${entityId}`
  }

  private async getSkipEntry(key: string): Promise<SkipEntry | null> {
    try {
      const value = await this.kv.get(key)
      if (!value) return null
      
      return JSON.parse(value) as SkipEntry
    } catch (error) {
      logger.warn('Failed to parse skip entry', { key, error })
      return null
    }
  }

  private isExpired(entry: SkipEntry): boolean {
    return new Date() > new Date(entry.expiresAt)
  }

  private getWindowForOperation(
    entityType: SkipEntry['entityType'],
    operation: SkipEntry['operation']
  ): number {
    switch (`${entityType}_${operation}`) {
      case 'patient_create':
        return this.config.patientCreateWindow
      case 'patient_update':
        return this.config.patientUpdateWindow
      case 'appointment_create':
        return this.config.appointmentCreateWindow
      case 'appointment_update':
        return this.config.appointmentUpdateWindow
      case 'appointment_delete':
        return this.config.appointmentDeleteWindow
      case 'invoice_create':
        return this.config.invoiceProcessWindow
      case 'payment_create':
        return this.config.paymentProcessWindow
      default:
        return 60 // Default 1 minute
    }
  }

  /**
   * Clean up expired entries (optional maintenance function)
   */
  async cleanupExpiredEntries(location: string): Promise<number> {
    // Note: KV automatically handles TTL, but this could be used for manual cleanup
    // Implementation would require listing keys with prefix and checking expiration
    logger.info('Skip logic cleanup requested', { location })
    return 0 // KV handles TTL automatically
  }
}

// Export singleton instance
export const skipLogic = new SkipLogicService()
```

### 3. Create Skip Logic Middleware
Create `src/middlewares/skipLogicMiddleware.ts`:

```typescript
import type { Context, Next } from 'hono'
import { skipLogic } from '@/services/skipLogic'
import { logger } from '@utils'

export interface SkipLogicContext {
  entityType: 'patient' | 'appointment' | 'invoice' | 'payment'
  entityId: string | number
  operation: 'create' | 'update' | 'delete'
}

export const withSkipLogic = (getContext: (c: Context) => SkipLogicContext) => {
  return async (c: Context, next: Next) => {
    const requestId = c.get('requestId') || crypto.randomUUID()
    const location = c.req.param('location') || 'unknown'
    
    try {
      const skipContext = getContext(c)
      
      const skipResult = await skipLogic.shouldSkip(
        skipContext.entityType,
        skipContext.entityId,
        skipContext.operation,
        location,
        requestId
      )
      
      if (skipResult.shouldSkip) {
        logger.info('Request skipped due to recent processing', {
          requestId,
          reason: skipResult.reason,
          skipContext
        })
        
        return c.json({
          success: true,
          message: skipResult.reason,
          skipped: true,
          requestId,
          processedAt: new Date().toISOString()
        }, 200)
      }
      
      // Store skip context for post-processing
      c.set('skipContext', skipContext)
      
      await next()
      
      // Record processing after successful completion
      await skipLogic.recordProcessing(
        skipContext.entityType,
        skipContext.entityId,
        skipContext.operation,
        location,
        requestId
      )
      
    } catch (error) {
      logger.error('Skip logic middleware error', {
        requestId,
        error: error instanceof Error ? error.message : String(error)
      })
      
      // Continue processing on skip logic errors
      await next()
    }
  }
}
```

## Dependencies
- **Prerequisite**: Task 1 (Webhook Routing Infrastructure)
- **Integrates with**: All job processor tasks in Sprint 2-4

## Testing Strategy
```typescript
// Test file: src/services/__tests__/skipLogic.test.ts
describe('SkipLogicService', () => {
  test('should skip duplicate patient creation', async () => {
    await skipLogic.recordProcessing('patient', 123, 'create', 'test-location', 'req-1')
    
    const result = await skipLogic.shouldSkip('patient', 123, 'create', 'test-location', 'req-2')
    expect(result.shouldSkip).toBe(true)
  })
  
  test('should not skip after expiration', async () => {
    // Test with very short window or mock time
  })
})
```

## Implementation Notes
- Use Cloudflare KV for distributed skip logic across workers
- Implement proper TTL to automatically clean up old entries
- Provide legacy compatibility methods for existing code patterns
- Handle KV failures gracefully to not block processing
