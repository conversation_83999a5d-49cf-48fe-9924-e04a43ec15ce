# Task 3: Create Database Timestamp-Based Skip Logic

## Description

Implement skip logic using database `apUpdatedAt` and `ccUpdatedAt` fields to prevent duplicate processing and sync loops. This uses the existing database schema's dedicated timestamp fields for each platform.

## Acceptance Criteria

- [ ] Skip logic using dedicated `apUpdatedAt` and `ccUpdatedAt` database fields
- [ ] Bidirectional duplicate prevention for both CC and AP webhooks
- [ ] Loop prevention using platform-specific timestamps
- [ ] Integration with existing database schema
- [ ] Proper error handling for database operations
- [ ] Never use `any` or `as any` - use proper TypeScript types
- [ ] Legacy compatibility methods for existing job processors

## Technical Implementation Details

### 1. Create Skip Logic Types

Create `src/types/skipLogic.ts`:

```typescript
import type { CCWebhookEvent, APWebhookEvent } from "./webhooks";

export interface SkipCheckResult {
  skip: boolean;
  reason?: string;
  existingTimestamp?: string;
  incomingTimestamp?: string;
}

export interface EntityTimestampData {
  entityType: "patient" | "appointment" | "invoice" | "payment";
  entityId: number;
  ccUpdatedAt?: string;
  apUpdatedAt?: string;
  ccData?: Record<string, unknown>;
  apData?: Record<string, unknown>;
}

export interface SkipLogicConfig {
  // Minimum time difference in seconds to consider an update significant
  minimumUpdateInterval: number;
  // Grace period for clock skew between systems
  clockSkewGracePeriod: number;
}
```

### 2. Create Database-Based Skip Logic Service

Create `src/services/skipLogicService.ts`:

```typescript
import type {
  CCWebhookEvent,
  APWebhookEvent,
  SkipCheckResult,
  SkipLogicConfig,
  EntityTimestampData,
} from "@/types/skipLogic";
import { logger, ErrorLogger } from "@utils";
import { db } from "@/database";
import { contacts, appointments, invoices, payments } from "@/database/schema";
import { eq } from "drizzle-orm";

export class SkipLogicService {
  private config: SkipLogicConfig = {
    minimumUpdateInterval: 30, // 30 seconds minimum between updates
    clockSkewGracePeriod: 60, // 60 seconds grace period for clock differences
  };

  /**
   * Check if a CliniCore webhook event should be skipped using ccUpdatedAt field
   */
  async shouldSkipCCEvent(
    event: CCWebhookEvent,
    requestId: string
  ): Promise<SkipCheckResult> {
    try {
      const entityType = event.type;
      const entityId = event.id;
      const incomingTimestamp = this.extractCCTimestamp(event.payload);

      if (!incomingTimestamp) {
        logger.warn("No updatedAt timestamp found in CC webhook payload", {
          requestId,
          entityType,
          entityId,
        });
        return { skip: false, reason: "No timestamp available for comparison" };
      }

      const existingData = await this.getExistingEntityData(
        entityType,
        entityId,
        "cc"
      );

      if (!existingData) {
        logger.info("Entity not found in database, processing CC webhook", {
          requestId,
          entityType,
          entityId,
        });
        return { skip: false, reason: "Entity not found in database" };
      }

      // Compare with ccUpdatedAt field
      const shouldSkip = this.compareTimestamps(
        incomingTimestamp,
        existingData.ccUpdatedAt,
        requestId,
        "CliniCore"
      );

      if (shouldSkip.skip) {
        logger.info("CC webhook skipped due to timestamp comparison", {
          requestId,
          entityType,
          entityId,
          incomingTimestamp,
          existingCCTimestamp: existingData.ccUpdatedAt,
          reason: shouldSkip.reason,
        });
      }

      return shouldSkip;
    } catch (error) {
      await ErrorLogger.logError(
        "CCSkipLogicCheck",
        error,
        {
          entityType: event.type,
          entityId: event.id,
          requestId,
        },
        "SkipLogicService"
      );

      // On error, don't skip - better to process than miss updates
      return {
        skip: false,
        reason: "Skip check failed, proceeding with processing",
      };
    }
  }

  /**
   * Check if an AutoPatient webhook event should be skipped using apUpdatedAt field
   */
  async shouldSkipAPEvent(
    event: APWebhookEvent,
    requestId: string
  ): Promise<SkipCheckResult> {
    try {
      const appointmentId = event.calendar.appointmentId;
      const incomingTimestamp = event.calendar.startTime; // Use startTime as update indicator

      const existingData = await this.getExistingEntityData(
        "appointment",
        appointmentId,
        "ap"
      );

      if (!existingData) {
        logger.info(
          "Appointment not found in database, processing AP webhook",
          {
            requestId,
            appointmentId,
          }
        );
        return { skip: false, reason: "Appointment not found in database" };
      }

      // Compare with apUpdatedAt field
      const shouldSkip = this.compareTimestamps(
        incomingTimestamp,
        existingData.apUpdatedAt,
        requestId,
        "AutoPatient"
      );

      if (shouldSkip.skip) {
        logger.info("AP webhook skipped due to timestamp comparison", {
          requestId,
          appointmentId,
          incomingTimestamp,
          existingAPTimestamp: existingData.apUpdatedAt,
          reason: shouldSkip.reason,
        });
      }

      return shouldSkip;
    } catch (error) {
      await ErrorLogger.logError(
        "APSkipLogicCheck",
        error,
        {
          appointmentId: event.calendar.appointmentId,
          requestId,
        },
        "SkipLogicService"
      );

      // On error, don't skip - better to process than miss updates
      return {
        skip: false,
        reason: "Error during AP skip check, processing anyway",
      };
    }
  }

  /**
   * Legacy compatibility methods for existing job processors
   */

  /**
   * Check if a patient creation should be skipped (legacy compatibility)
   */
  async hasProcessPatientCreate(apId: string): Promise<boolean> {
    try {
      const existingContact = await db
        .select()
        .from(contacts)
        .where(eq(contacts.apId, apId))
        .limit(1);

      if (!existingContact.length) {
        return false; // No existing contact, don't skip
      }

      // Check if we have recent CC data that would indicate recent processing
      const contact = existingContact[0];
      if (!contact.ccUpdatedAt) {
        return false; // No CC timestamp, don't skip
      }

      // Skip if CC was updated very recently (within 30 seconds)
      const ccUpdatedTime = new Date(contact.ccUpdatedAt).getTime();
      const now = Date.now();
      const timeDiff = (now - ccUpdatedTime) / 1000;

      return timeDiff < this.config.minimumUpdateInterval;
    } catch (error) {
      logger.error("Error checking patient create skip logic", { apId, error });
      return false; // On error, don't skip
    }
  }

  /**
   * Check if an appointment operation should be skipped (legacy compatibility)
   */
  async hasProcessAppointment(
    appointmentId: string | number,
    operation: "create" | "update" | "delete"
  ): Promise<boolean> {
    try {
      const existingAppointment = await db
        .select()
        .from(appointments)
        .where(eq(appointments.ccId, Number(appointmentId)))
        .limit(1);

      if (!existingAppointment.length) {
        return false; // No existing appointment, don't skip
      }

      const appointment = existingAppointment[0];

      // For create operations, check if we have recent AP data
      if (operation === "create" && appointment.apUpdatedAt) {
        const apUpdatedTime = new Date(appointment.apUpdatedAt).getTime();
        const now = Date.now();
        const timeDiff = (now - apUpdatedTime) / 1000;
        return timeDiff < this.config.minimumUpdateInterval;
      }

      // For update operations, check if we have recent CC data
      if (operation === "update" && appointment.ccUpdatedAt) {
        const ccUpdatedTime = new Date(appointment.ccUpdatedAt).getTime();
        const now = Date.now();
        const timeDiff = (now - ccUpdatedTime) / 1000;
        return timeDiff < this.config.minimumUpdateInterval;
      }

      return false;
    } catch (error) {
      logger.error("Error checking appointment skip logic", {
        appointmentId,
        operation,
        error,
      });
      return false; // On error, don't skip
    }
  }

  /**
   * Helper methods for database operations and timestamp comparison
   */

  private async getExistingEntityData(
    entityType: string,
    entityId: string | number,
    platform: "cc" | "ap"
  ): Promise<EntityTimestampData | null> {
    try {
      switch (entityType) {
        case "patient":
          const contact = await db
            .select()
            .from(contacts)
            .where(eq(contacts.ccId, Number(entityId)))
            .limit(1);

          return contact.length
            ? {
                entityType: "patient",
                entityId: Number(entityId),
                ccUpdatedAt: contact[0].ccUpdatedAt,
                apUpdatedAt: contact[0].apUpdatedAt,
                ccData: contact[0].ccData,
                apData: contact[0].apData,
              }
            : null;

        case "appointment":
          const appointment = await db
            .select()
            .from(appointments)
            .where(
              platform === "cc"
                ? eq(appointments.ccId, Number(entityId))
                : eq(appointments.apId, String(entityId))
            )
            .limit(1);

          return appointment.length
            ? {
                entityType: "appointment",
                entityId: Number(entityId),
                ccUpdatedAt: appointment[0].ccUpdatedAt,
                apUpdatedAt: appointment[0].apUpdatedAt,
                ccData: appointment[0].ccData,
                apData: appointment[0].apData,
              }
            : null;

        default:
          logger.warn("Unsupported entity type for skip logic", { entityType });
          return null;
      }
    } catch (error) {
      logger.error("Error fetching entity data for skip logic", {
        entityType,
        entityId,
        platform,
        error,
      });
      return null;
    }
  }

  private extractCCTimestamp(payload: any): string | null {
    // Extract updatedAt from CliniCore payload
    return payload?.updatedAt || null;
  }

  private compareTimestamps(
    incomingTimestamp: string,
    existingTimestamp: string | null,
    requestId: string,
    platform: string
  ): SkipCheckResult {
    if (!existingTimestamp) {
      return { skip: false, reason: `No existing ${platform} timestamp found` };
    }

    try {
      const incomingTime = new Date(incomingTimestamp).getTime();
      const existingTime = new Date(existingTimestamp).getTime();
      const timeDiff = (incomingTime - existingTime) / 1000;

      // Skip if incoming timestamp is older or too close to existing
      if (timeDiff <= 0) {
        return {
          skip: true,
          reason: `${platform} webhook timestamp is older than or equal to existing record`,
          incomingTimestamp,
          existingTimestamp,
        };
      }

      if (timeDiff < this.config.minimumUpdateInterval) {
        return {
          skip: true,
          reason: `${platform} webhook received too soon after last update (${timeDiff}s < ${this.config.minimumUpdateInterval}s)`,
          incomingTimestamp,
          existingTimestamp,
        };
      }

      return {
        skip: false,
        reason: `${platform} webhook timestamp is newer and outside minimum interval`,
        incomingTimestamp,
        existingTimestamp,
      };
    } catch (error) {
      logger.error("Error comparing timestamps", {
        incomingTimestamp,
        existingTimestamp,
        platform,
        requestId,
        error,
      });
      return {
        skip: false,
        reason: "Error comparing timestamps, processing anyway",
      };
    }
  }
}

// Export singleton instance
export const skipLogic = new SkipLogicService();
```

### 3. Update Database Schema Integration

The skip logic service integrates with the existing database schema fields:

```typescript
// Database schema fields used for skip logic (already exists in src/database/schema.ts)
export const contacts = pgTable("contacts", {
  // ... other fields
  ccUpdatedAt: timestamp("cc_updated_at"),
  apUpdatedAt: timestamp("ap_updated_at"),
  ccData: jsonb("cc_data"),
  apData: jsonb("ap_data"),
});

export const appointments = pgTable("appointments", {
  // ... other fields
  ccUpdatedAt: timestamp("cc_updated_at"),
  apUpdatedAt: timestamp("ap_updated_at"),
  ccData: jsonb("cc_data"),
  apData: jsonb("ap_data"),
});
```

## Dependencies

- **Prerequisite**: Task 1 (Webhook Routing Infrastructure)
- **Integrates with**: All webhook handlers and job processors
- **Database**: Uses existing `ccUpdatedAt` and `apUpdatedAt` fields

## Testing Strategy

```typescript
// Test file: src/services/__tests__/skipLogicService.test.ts
describe("SkipLogicService", () => {
  test("should skip CC webhook with older timestamp", async () => {
    // Setup existing contact with recent ccUpdatedAt
    const contact = await db.insert(contacts).values({
      ccId: 123,
      ccUpdatedAt: new Date().toISOString(),
      // ... other fields
    });

    const event: CCWebhookEvent = {
      type: "patient",
      id: 123,
      payload: {
        id: 123,
        updatedAt: new Date(Date.now() - 60000).toISOString(), // 1 minute ago
        // ... other fields
      },
    };

    const result = await skipLogic.shouldSkipCCEvent(event, "test-req");
    expect(result.skip).toBe(true);
  });

  test("should process CC webhook with newer timestamp", async () => {
    // Test with newer timestamp
  });

  test("should handle legacy compatibility methods", async () => {
    const shouldSkip = await skipLogic.hasProcessPatientCreate("ap-123");
    expect(typeof shouldSkip).toBe("boolean");
  });
});
```

## Implementation Notes

- Uses dedicated `ccUpdatedAt` and `apUpdatedAt` database fields
- Prevents sync loops between CliniCore and AutoPatient platforms
- Maintains legacy compatibility for existing job processors
- Graceful error handling - defaults to processing on errors
- Configurable time intervals for different scenarios
- Direct database queries for optimal performance
