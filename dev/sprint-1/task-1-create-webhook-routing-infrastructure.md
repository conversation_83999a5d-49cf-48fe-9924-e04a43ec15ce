# Task 1: Create Webhook Routing Infrastructure

## Description

Set up Hono routes to handle CliniCore (CC) webhook events with proper validation and routing logic. This forms the foundation for receiving webhook events from the CliniCore platform to replace the legacy Socket.io connection.

## Acceptance Criteria

- [ ] Webhook endpoints created for CliniCore platform events
- [ ] Request body validation implemented
- [ ] Error handling for malformed requests
- [ ] Logging for incoming webhook events
- [ ] Never use `any` or `as any` - use proper TypeScript types
- [ ] Direct webhook processing without job queues

## Technical Implementation Details

### 1. Create Webhook Route Structure

Create the main webhook routing in `src/index.ts`:

```typescript
import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { ccWebhookProcessor } from "./webhooks/ccProcessor";
import { webhookValidator } from "./middlewares/webhookValidator";

const app = new Hono<Env>();
app.use(contextStorage());

// CliniCore Platform webhook endpoint (no location parameter)
app.post("/webhooks/cc", webhookValidator, ccWebhookProcessor);

// Health check endpoint
app.get("/health", (c) =>
  c.json({ status: "ok", timestamp: new Date().toISOString() })
);

export default app;
```

### 2. Create Webhook Types

Create `src/types/webhooks.ts`:

```typescript
export interface CCWebhookEvent {
  event:
    | "EntityWasCreated"
    | "EntityWasUpdated"
    | "EntityWasDeleted"
    | "AppointmentWasCreated";
  model: "Patient" | "Appointment" | "Invoice" | "Payment";
  id: number;
  payload: Record<string, unknown>;
}

export interface WebhookContext {
  event: CCWebhookEvent;
  requestId: string;
}

export interface WebhookResponse {
  success: boolean;
  message: string;
  requestId: string;
  processedAt: string;
  data?: Record<string, unknown>;
}
```

### 3. Create CliniCore Webhook Processor

Create `src/webhooks/ccProcessor.ts`:

```typescript
import type { Context } from "hono";
import type { CCWebhookEvent, WebhookResponse } from "@/types/webhooks";
import { logger } from "@utils";
import { createError } from "@utils";
import { ccEventRouter } from "./ccEventRouter";

export const ccWebhookProcessor = async (c: Context): Promise<Response> => {
  const requestId = crypto.randomUUID();

  try {
    const body = (await c.req.json()) as CCWebhookEvent;

    logger.info("CliniCore webhook received", {
      requestId,
      event: body.event,
      model: body.model,
      id: body.id,
    });

    // Validate required fields
    if (!body.event || !body.model || !body.id) {
      throw createError("Missing required fields: event, model, or id", 400);
    }

    // Process the event directly (no job queue)
    const result = await ccEventRouter.processEvent(body, requestId);

    const response: WebhookResponse = {
      success: result.success,
      message: result.message,
      requestId,
      processedAt: new Date().toISOString(),
      data: result.data,
    };

    return c.json(response, result.success ? 200 : 500);
  } catch (error) {
    logger.error("CliniCore webhook processing failed", {
      requestId,
      error: error instanceof Error ? error.message : String(error),
    });

    const response: WebhookResponse = {
      success: false,
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
      requestId,
      processedAt: new Date().toISOString(),
    };

    const statusCode =
      error instanceof Error && "status" in error
        ? (error as { status: number }).status
        : 500;
    return c.json(response, statusCode);
  }
};
```

## Dependencies

- **Prerequisite**: None (foundational task)
- **Blocks**: Task 2 (CliniCore Event Router), Task 5 (Webhook Validation Middleware)

## Testing Strategy

```typescript
// Test file: src/webhooks/__tests__/routing.test.ts
describe("CliniCore Webhook Routing", () => {
  test("should accept valid CC webhook", async () => {
    const response = await app.request("/webhooks/cc", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        event: "EntityWasCreated",
        model: "Patient",
        id: 123,
        payload: { firstName: "John" },
      }),
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.success).toBe(true);
  });
});
```

## Implementation Notes

- Use proper TypeScript interfaces for all webhook data
- Implement comprehensive error handling
- Add request ID tracking for debugging
- Follow existing project structure and naming conventions
- Ensure all routes are properly typed with Hono context
- Process webhooks directly without job queues for immediate response
