# Task 1: Create Webhook Routing Infrastructure

## Description

Set up Hono routes to handle CliniCore (CC) webhook events with proper validation and routing logic. This forms the foundation for receiving webhook events from the CliniCore platform to replace the legacy Socket.io connection.

## Acceptance Criteria

- [ ] Webhook endpoints created for CliniCore platform events
- [ ] Request body validation implemented
- [ ] Error handling for malformed requests
- [ ] Logging for incoming webhook events
- [ ] Never use `any` or `as any` - use proper TypeScript types
- [ ] Direct webhook processing without job queues

## Technical Implementation Details

### 1. Create Webhook Route Structure

Create the main webhook routing in `src/index.ts`:

```typescript
import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { ccWebhookProcessor } from "./webhooks/ccProcessor";
import { webhookValidator } from "./middlewares/webhookValidator";

const app = new Hono<Env>();
app.use(contextStorage());

// CliniCore Platform webhook endpoint (no location parameter)
app.post("/webhooks/cc", webhookValidator, ccWebhookProcessor);

// Health check endpoint
app.get("/health", (c) =>
  c.json({ status: "ok", timestamp: new Date().toISOString() })
);

export default app;
```

### 2. Create Webhook Types

Create `src/types/webhooks.ts`:

```typescript
// CliniCore Socket.io event structure (converted to webhook)
export interface CCWebhookEvent {
  type: "patient" | "appointment" | "invoice" | "payment";
  payload:
    | CCPatientPayload
    | CCAppointmentPayload
    | CCInvoicePayload
    | CCPaymentPayload;
  id: number;
}

// Exact payload types based on legacy Socket.io data
export interface CCPatientPayload {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  firstName: string;
  lastName: string;
  dob: string | null;
  ssn: string | null;
  flashMessage: string;
  active: boolean;
  phoneMobile: string;
  email: string;
  title: string | null;
  titleSuffix: string | null;
  healthInsurance: string | null;
  gender: string | null;
  addresses: any[];
  categories: number[];
  customFields: number[];
  invoices: number[];
  payments: number[];
  files: number[];
  history: number[];
  appointments: number[];
  messages: any[];
  medications: any[];
  qrUrl: string;
  avatarUrl: string | null;
}

export interface CCAppointmentPayload {
  id: number;
  uuid: string | null;
  startsAt: string;
  endsAt: string;
  arrivedAt: string | null;
  processedAt: string | null;
  treatmentStartedAt: string | null;
  allDay: boolean;
  slot: boolean;
  subject: string | null;
  title: string;
  firstOfPatient: boolean;
  onPatientBirthday: boolean;
  description: string;
  color: string | null;
  patientCategories: number[];
  patientsPreview: any[];
  patients: number[];
  people: number[];
  resources: number[];
  categories: number[];
  location?: number;
  services: number[];
  series: number;
  canceledWhy: string | null;
  createdAt: string;
  updatedAt: string;
  canceledAt: string | null;
  createdBy: number;
  updatedBy: number;
  canceledBy: number | null;
  reminderAt: string | null;
  reminderStatus: string;
  reminderSentAt: string | null;
  deletedAt: string | null;
}

export interface CCInvoicePayload {
  id: number;
  invoiceNumber?: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
  addressText?: null;
  discount?: number;
  status?: string;
  vatId?: null;
  description?: string;
  note?: null;
  canceledWhy?: null;
  settlementStatus?: null;
  pdfUrl?: string;
  invoiceNumberSequence?: string;
  address?: {
    id?: number;
    label?: null;
    name?: string;
    street?: null;
    streetNumber?: null;
    postalCode?: null;
    city?: null;
    country?: string;
    primary?: number;
  };
  appointment?: null;
  positions?: {
    id?: number;
    name?: string;
    gross?: number;
    discount?: number;
    [key: string]: any;
  }[];
  reversedBy?: null;
  reverses?: null;
  patient?: number;
  payments?: {
    id?: number;
    [key: string]: any;
  }[];
  practicioner?: number;
  settlement?: null;
  sentAt?: null;
  wahonlinedAt?: null;
  diagnoses?: {
    id?: number;
    [key: string]: any;
  }[];
}

export interface CCPaymentPayload {
  id: number;
  paymentNumber: string;
  gross: number;
  customIdentification: null;
  comment: string;
  createdAt: string;
  date: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  register: number;
  patient: number;
  invoicePayments: {
    id: number;
    gross: number;
    invoice: number;
    payment: number;
  }[];
  reversedBy: null;
  reverses: null;
  pdfUrl: string;
  canceled: boolean;
}

// AutoPatient webhook structure (for bidirectional sync)
export interface APWebhookEvent {
  calendar: {
    id: string;
    appointmentId: string;
    startTime: string;
    endTime: string;
    status: string;
    created_by_meta: {
      source: string;
      channel?: string;
    };
    last_updated_by_meta?: {
      source: string;
      channel?: string;
    };
  };
  contact_id: string;
  email?: string;
  phone?: string;
}

export interface WebhookContext {
  event: CCWebhookEvent;
  requestId: string;
}

export interface WebhookResponse {
  success: boolean;
  message: string;
  requestId: string;
  processedAt: string;
  data?: Record<string, unknown>;
}
```

### 3. Create CliniCore Webhook Processor

Create `src/webhooks/ccProcessor.ts`:

```typescript
import type { Context } from "hono";
import type { CCWebhookEvent, WebhookResponse } from "@/types/webhooks";
import { logger } from "@utils";
import { createError } from "@utils";
import { ccEventRouter } from "./ccEventRouter";

export const ccWebhookProcessor = async (c: Context): Promise<Response> => {
  const requestId = crypto.randomUUID();

  try {
    const body = (await c.req.json()) as CCWebhookEvent;

    logger.info("CliniCore webhook received", {
      requestId,
      type: body.type,
      id: body.id,
      payloadKeys: Object.keys(body.payload || {}),
    });

    // Validate required fields
    if (!body.type || !body.payload || !body.id) {
      throw createError("Missing required fields: type, payload, or id", 400);
    }

    // Process the event directly (no job queue)
    const result = await ccEventRouter.processEvent(body, requestId);

    const response: WebhookResponse = {
      success: result.success,
      message: result.message,
      requestId,
      processedAt: new Date().toISOString(),
      data: result.data,
    };

    return c.json(response, result.success ? 200 : 500);
  } catch (error) {
    logger.error("CliniCore webhook processing failed", {
      requestId,
      error: error instanceof Error ? error.message : String(error),
    });

    const response: WebhookResponse = {
      success: false,
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
      requestId,
      processedAt: new Date().toISOString(),
    };

    const statusCode =
      error instanceof Error && "status" in error
        ? (error as { status: number }).status
        : 500;
    return c.json(response, statusCode);
  }
};
```

### 4. Update Main Index File

Update `src/index.ts` to include bidirectional webhook routing:

```typescript
import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { ccWebhookProcessor } from "./webhooks/ccProcessor";
import { apWebhookProcessor } from "./webhooks/apProcessor";
import { webhookValidator } from "./middlewares/webhookValidator";

const app = new Hono<Env>();
app.use(contextStorage());

app.onError((err, c) => {
  console.error(err);
  return c.json(
    {
      message: "Internal Server Error",
      requestId: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
    },
    500
  );
});

// CliniCore webhook endpoint (replaces Socket.io)
app.post("/webhooks/cc", webhookValidator, ccWebhookProcessor);

// AutoPatient webhook endpoints (bidirectional sync)
app.post(
  "/webhooks/ap/appointment/creates",
  webhookValidator,
  apWebhookProcessor
);
app.post(
  "/webhooks/ap/appointment/updates",
  webhookValidator,
  apWebhookProcessor
);

// Health check
app.get("/", (c) => c.text("DermaCare Data Sync Service - OK"));
app.get("/health", (c) =>
  c.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "2.0.0",
  })
);

export default app;
```

## Dependencies

- **Prerequisite**: None (foundational task)
- **Blocks**: Task 2 (CliniCore Event Router), Task 5 (Webhook Validation Middleware)

## Testing Strategy

```typescript
// Test file: src/webhooks/__tests__/routing.test.ts
describe("CliniCore Webhook Routing", () => {
  test("should accept valid CC webhook", async () => {
    const response = await app.request("/webhooks/cc", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        type: "patient",
        id: 123,
        payload: {
          id: 123,
          firstName: "John",
          lastName: "Doe",
          email: "<EMAIL>",
          phoneMobile: "+**********",
          updatedAt: "2024-01-01T10:00:00Z",
        },
      }),
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.success).toBe(true);
  });
});
```

## Implementation Notes

- Use proper TypeScript interfaces for all webhook data
- Implement comprehensive error handling
- Add request ID tracking for debugging
- Follow existing project structure and naming conventions
- Ensure all routes are properly typed with Hono context
- Process webhooks directly without job queues for immediate response
