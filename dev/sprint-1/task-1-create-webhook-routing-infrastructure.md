# Task 1: Create Webhook Routing Infrastructure

## Description

Set up Hono routes to handle CC webhook events with proper validation and routing logic. This forms the foundation for receiving webhook events from the CC platform to replace the legacy Socket.io connection.

## Acceptance Criteria

- [ ] Webhook endpoints created for CC platform events
- [ ] Route parameters properly extracted (location, event type)
- [ ] Request body validation implemented
- [ ] Error handling for malformed requests
- [ ] Logging for incoming webhook events
- [ ] Never use `any` or `as any` - use proper TypeScript types

## Technical Implementation Details

### 1. Create Webhook Route Structure

Create the main webhook routing in `src/index.ts`:

```typescript
import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { webhookProcessor } from "./webhooks/processor";
import { webhookValidator } from "./middlewares/webhookValidator";

const app = new Hono<Env>();
app.use(contextStorage());

// CC Platform webhook endpoint
app.post("/webhooks/cc/:location", webhookValidator, webhookProcessor);

// Health check endpoint
app.get("/health", (c) =>
  c.json({ status: "ok", timestamp: new Date().toISOString() })
);

export default app;
```

### 2. Create Webhook Types

Create `src/types/webhooks.ts`:

```typescript
export interface CCWebhookEvent {
  event:
    | "EntityWasCreated"
    | "EntityWasUpdated"
    | "EntityWasDeleted"
    | "AppointmentWasCreated";
  model: "Patient" | "Appointment" | "Invoice" | "Payment";
  id: number;
  payload: Record<string, unknown>;
  timestamp?: string;
  location?: string;
}

export interface WebhookContext {
  location: string;
  event: CCWebhookEvent;
  requestId: string;
}

export interface WebhookResponse {
  success: boolean;
  message: string;
  requestId: string;
  processedAt: string;
  data?: Record<string, unknown>;
}
```

### 3. Create Webhook Processor

Create `src/webhooks/processor.ts`:

```typescript
import type { Context } from "hono";
import type { CCWebhookEvent, WebhookResponse } from "@/types/webhooks";
import { logger } from "@utils";
import { createError } from "@utils";

export const webhookProcessor = async (c: Context): Promise<Response> => {
  const requestId = crypto.randomUUID();
  const location = c.req.param("location");

  try {
    const body = (await c.req.json()) as CCWebhookEvent;

    logger.info("Webhook received", {
      requestId,
      location,
      event: body.event,
      model: body.model,
      id: body.id,
    });

    // Validate required fields
    if (!body.event || !body.model || !body.id) {
      throw createError("Missing required fields: event, model, or id", 400);
    }

    // Route to appropriate processor based on event and model
    const result = await routeWebhookEvent(body, location, requestId);

    const response: WebhookResponse = {
      success: true,
      message: result.message || "Event processed successfully",
      requestId,
      processedAt: new Date().toISOString(),
      data: result.data,
    };

    return c.json(response, 200);
  } catch (error) {
    logger.error("Webhook processing failed", {
      requestId,
      location,
      error: error instanceof Error ? error.message : String(error),
    });

    const response: WebhookResponse = {
      success: false,
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
      requestId,
      processedAt: new Date().toISOString(),
    };

    const statusCode =
      error instanceof Error && "status" in error
        ? (error as { status: number }).status
        : 500;
    return c.json(response, statusCode);
  }
};

async function routeWebhookEvent(
  event: CCWebhookEvent,
  location: string,
  requestId: string
): Promise<{ message: string; data?: Record<string, unknown> }> {
  // This will be implemented in subsequent tasks
  // For now, just log and return success
  logger.info("Event routed", {
    event: event.event,
    model: event.model,
    requestId,
  });

  return {
    message: `Event ${event.event} for ${event.model} queued for processing`,
    data: { eventId: event.id },
  };
}
```

## Dependencies

- **Prerequisite**: None (foundational task)
- **Blocks**: Task 2 (Webhook Event Processor), Task 5 (Webhook Validation Middleware)

## Testing Strategy

```typescript
// Test file: src/webhooks/__tests__/routing.test.ts
describe("Webhook Routing", () => {
  test("should accept valid CC webhook", async () => {
    const response = await app.request("/webhooks/cc/test-location", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        event: "EntityWasCreated",
        model: "Patient",
        id: 123,
        payload: { firstName: "John" },
      }),
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.success).toBe(true);
  });
});
```

## Implementation Notes

- Use proper TypeScript interfaces for all webhook data
- Implement comprehensive error handling
- Add request ID tracking for debugging
- Follow existing project structure and naming conventions
- Ensure all routes are properly typed with Hono context
