# Task 5: Create Webhook Validation Middleware

## Description

Implement middleware to validate incoming webhook payloads and authenticate requests. This ensures data integrity and security for all incoming webhook events from CliniCore and AutoPatient platforms.

## Acceptance Criteria

- [ ] Request payload validation using Zod schemas based on legacy payload structures
- [ ] Authentication/authorization for webhook endpoints
- [ ] Rate limiting to prevent abuse
- [ ] Request size limits and content type validation
- [ ] Never use `any` or `as any` - use proper TypeScript types
- [ ] Comprehensive error responses for validation failures

## Technical Implementation Details

### 1. Create Validation Schemas

Create `src/schemas/webhookSchemas.ts`:

```typescript
import { z } from "zod";

// Base CliniCore webhook event schema (based on legacy Socket.io structure)
export const CCWebhookEventSchema = z.object({
  event: z.enum([
    "EntityWasCreated",
    "EntityWasUpdated",
    "EntityWasDeleted",
    "AppointmentWasCreated",
  ]),
  model: z.enum(["Patient", "Appointment", "Invoice", "Payment"]),
  id: z.union([z.number(), z.string()]).transform((val) => Number(val)),
  payload: z.record(z.unknown()),
  timestamp: z.string().optional(),
});

// Patient payload schemas
export const CCPatientPayloadSchema = z.object({
  id: z.number(),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  email: z.string().email().optional(),
  phoneMobile: z.string().optional(),
  dob: z.string().optional(),
  gender: z.string().optional(),
  customFields: z.array(z.number()).optional(),
  appointments: z.array(z.number()).optional(),
  invoices: z.array(z.number()).optional(),
  payments: z.array(z.number()).optional(),
  updatedAt: z.string(),
  createdAt: z.string(),
});

// Appointment payload schemas
export const CCAppointmentPayloadSchema = z.object({
  id: z.number(),
  title: z.string().optional(),
  startsAt: z.string(),
  endsAt: z.string(),
  patients: z.array(z.number()),
  services: z.array(z.number()).optional(),
  canceledAt: z.string().optional(),
  canceledWhy: z.string().optional(),
  firstOfPatient: z.boolean().optional(),
  updatedAt: z.string(),
  createdAt: z.string(),
});

// Invoice payload schema
export const CCInvoicePayloadSchema = z.object({
  id: z.number(),
  patient: z.number(),
  pdfUrl: z.string().url().optional(),
  discount: z.number().optional(),
  status: z.string(),
  positions: z.array(
    z.object({
      name: z.string(),
      gross: z.number(),
      discount: z.number().optional(),
    })
  ),
  diagnoses: z
    .array(
      z.object({
        text: z.string(),
      })
    )
    .optional(),
  practicioner: z.number().optional(),
  updatedAt: z.string(),
  createdAt: z.string(),
});

// Payment payload schema
export const CCPaymentPayloadSchema = z.object({
  id: z.number(),
  patient: z.number(),
  gross: z.number(),
  canceled: z.boolean().optional(),
  reversedBy: z.number().optional(),
  reverses: z.number().optional(),
  updatedAt: z.string(),
  createdAt: z.string(),
});

// AP webhook schemas (for Sprint 5)
export const APWebhookEventSchema = z.object({
  calendar: z.object({
    id: z.string(),
    appointmentId: z.string(),
    startTime: z.string(),
    endTime: z.string(),
    status: z.string(),
    created_by_meta: z.object({
      source: z.string(),
      channel: z.string().optional(),
    }),
  }),
  contact_id: z.string(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
});
```

### 2. Create Webhook Validator Middleware

Create `src/middlewares/webhookValidator.ts`:

```typescript
import type { Context, Next } from "hono";
import { z } from "zod";
import { logger, createError } from "@utils";
import {
  CCWebhookEventSchema,
  CCPatientPayloadSchema,
  CCAppointmentPayloadSchema,
  CCInvoicePayloadSchema,
  CCPaymentPayloadSchema,
} from "@/schemas/webhookSchemas";

export interface ValidationContext {
  requestId: string;
  contentLength: number;
  userAgent?: string;
}

export const webhookValidator = async (c: Context, next: Next) => {
  const requestId = crypto.randomUUID();
  c.set("requestId", requestId);

  const contentType = c.req.header("content-type");
  const contentLength = parseInt(c.req.header("content-length") || "0");
  const userAgent = c.req.header("user-agent");

  const validationContext: ValidationContext = {
    requestId,
    contentLength,
    userAgent,
  };

  try {
    // 1. Validate content type
    if (!contentType?.includes("application/json")) {
      throw createError("Content-Type must be application/json", 400);
    }

    // 2. Validate content length
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (contentLength > maxSize) {
      throw createError(
        `Request body too large. Maximum size: ${maxSize} bytes`,
        413
      );
    }

    // 3. Basic request validation (no location parameter for CC webhooks)

    // 4. Parse and validate JSON body
    let body: unknown;
    try {
      body = await c.req.json();
    } catch (error) {
      throw createError("Invalid JSON in request body", 400);
    }

    // 5. Validate webhook event structure
    const webhookEvent = CCWebhookEventSchema.parse(body);

    // 6. Validate payload based on model type
    const validatedPayload = await validatePayloadByModel(
      webhookEvent,
      validationContext
    );

    // 7. Store validated data in context
    c.set("validatedWebhook", {
      ...webhookEvent,
      payload: validatedPayload,
    });

    c.set("validationContext", validationContext);

    logger.info("CliniCore webhook validation successful", {
      requestId,
      event: webhookEvent.event,
      model: webhookEvent.model,
      id: webhookEvent.id,
    });

    await next();
  } catch (error) {
    logger.warn("Webhook validation failed", {
      ...validationContext,
      error: error instanceof Error ? error.message : String(error),
    });

    if (error instanceof z.ZodError) {
      const validationErrors = error.errors.map((err) => ({
        path: err.path.join("."),
        message: err.message,
        code: err.code,
      }));

      return c.json(
        {
          success: false,
          message: "Validation failed",
          errors: validationErrors,
          requestId,
          timestamp: new Date().toISOString(),
        },
        400
      );
    }

    const statusCode =
      error instanceof Error && "status" in error
        ? (error as { status: number }).status
        : 400;

    return c.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Validation failed",
        requestId,
        timestamp: new Date().toISOString(),
      },
      statusCode
    );
  }
};

async function validatePayloadByModel(
  webhookEvent: z.infer<typeof CCWebhookEventSchema>,
  context: ValidationContext
): Promise<Record<string, unknown>> {
  const { model, payload } = webhookEvent;

  try {
    switch (model) {
      case "Patient":
        return CCPatientPayloadSchema.parse(payload);

      case "Appointment":
        return CCAppointmentPayloadSchema.parse(payload);

      case "Invoice":
        return CCInvoicePayloadSchema.parse(payload);

      case "Payment":
        return CCPaymentPayloadSchema.parse(payload);

      default:
        logger.warn("Unknown model type, skipping payload validation", {
          requestId: context.requestId,
          model,
        });
        return payload as Record<string, unknown>;
    }
  } catch (error) {
    logger.error("Payload validation failed", {
      requestId: context.requestId,
      model,
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
}
```

### 3. Create Rate Limiting Middleware

Create `src/middlewares/rateLimiter.ts`:

```typescript
import type { Context, Next } from "hono";
import { getKV, logger } from "@utils";

export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator?: (c: Context) => string;
}

export const createRateLimiter = (config: RateLimitConfig) => {
  const kv = getKV();

  return async (c: Context, next: Next) => {
    const key = config.keyGenerator
      ? config.keyGenerator(c)
      : `rate_limit:${c.req.header("cf-connecting-ip") || "unknown"}`;

    const requestId = c.get("requestId") || crypto.randomUUID();

    try {
      const now = Date.now();
      const windowStart = now - config.windowMs;

      // Get current request count
      const currentCount = await getCurrentRequestCount(kv, key, windowStart);

      if (currentCount >= config.maxRequests) {
        logger.warn("Rate limit exceeded", {
          requestId,
          key,
          currentCount,
          maxRequests: config.maxRequests,
        });

        return c.json(
          {
            success: false,
            message: "Rate limit exceeded",
            retryAfter: Math.ceil(config.windowMs / 1000),
            requestId,
            timestamp: new Date().toISOString(),
          },
          429
        );
      }

      // Record this request
      await recordRequest(kv, key, now, config.windowMs);

      await next();
    } catch (error) {
      logger.error("Rate limiting error", {
        requestId,
        error: error instanceof Error ? error.message : String(error),
      });

      // Continue on rate limiting errors
      await next();
    }
  };
};

async function getCurrentRequestCount(
  kv: KVNamespace,
  key: string,
  windowStart: number
): Promise<number> {
  const value = await kv.get(`${key}:count`);
  if (!value) return 0;

  const data = JSON.parse(value) as { count: number; firstRequest: number };

  // If the window has expired, reset count
  if (data.firstRequest < windowStart) {
    return 0;
  }

  return data.count;
}

async function recordRequest(
  kv: KVNamespace,
  key: string,
  now: number,
  windowMs: number
): Promise<void> {
  const countKey = `${key}:count`;
  const existing = await kv.get(countKey);

  let count = 1;
  let firstRequest = now;

  if (existing) {
    const data = JSON.parse(existing) as {
      count: number;
      firstRequest: number;
    };
    const windowStart = now - windowMs;

    if (data.firstRequest >= windowStart) {
      count = data.count + 1;
      firstRequest = data.firstRequest;
    }
  }

  await kv.put(countKey, JSON.stringify({ count, firstRequest }), {
    expirationTtl: Math.ceil(windowMs / 1000) + 60, // Add buffer
  });
}

// Default rate limiter for webhooks
export const webhookRateLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 100, // 100 requests per minute per IP
  keyGenerator: (c) => {
    const ip = c.req.header("cf-connecting-ip") || "unknown";
    return `webhook_rate_limit:${ip}`;
  },
});
```

### 4. Update Main Application

Update `src/index.ts` to include validation middleware:

```typescript
import { webhookValidator } from "./middlewares/webhookValidator";
import { webhookRateLimiter } from "./middlewares/rateLimiter";

// Apply middleware to CliniCore webhook route
app.post(
  "/webhooks/cc",
  webhookRateLimiter,
  webhookValidator,
  ccWebhookProcessor
);
```

## Dependencies

- **Prerequisite**: Task 1 (Webhook Routing Infrastructure)
- **Integrates with**: Task 2 (Webhook Event Processor)

## Testing Strategy

```typescript
// Test file: src/middlewares/__tests__/webhookValidator.test.ts
describe("Webhook Validator", () => {
  test("should validate correct patient webhook", async () => {
    const validPayload = {
      event: "EntityWasCreated",
      model: "Patient",
      id: 123,
      payload: {
        id: 123,
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        updatedAt: "2023-01-01T00:00:00Z",
        createdAt: "2023-01-01T00:00:00Z",
      },
    };

    // Test validation passes
  });

  test("should reject invalid payload", async () => {
    const invalidPayload = {
      event: "InvalidEvent",
      model: "Patient",
      id: "invalid",
    };

    // Test validation fails with proper error
  });
});
```

## Implementation Notes

- Use Zod for runtime type validation and TypeScript integration
- Implement comprehensive error messages for debugging
- Add rate limiting to prevent abuse
- Follow security best practices for webhook validation
- Ensure all validation errors are properly logged
