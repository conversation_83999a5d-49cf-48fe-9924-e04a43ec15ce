# Task 5: Create Webhook Validation Middleware

## Description

Implement middleware to validate incoming webhook payloads and authenticate requests. This ensures data integrity and security for all incoming webhook events from CliniCore and AutoPatient platforms.

## Acceptance Criteria

- [ ] Request payload validation using Zod schemas based on legacy payload structures
- [ ] Authentication/authorization for webhook endpoints
- [ ] Rate limiting to prevent abuse
- [ ] Request size limits and content type validation
- [ ] Never use `any` or `as any` - use proper TypeScript types
- [ ] Comprehensive error responses for validation failures

## Technical Implementation Details

### 1. Create Validation Schemas

Create `src/schemas/webhookSchemas.ts`:

```typescript
import { z } from "zod";

// CliniCore webhook event schema (same as legacy Socket.io structure)
export const CCWebhookEventSchema = z.object({
  event: z.enum([
    "EntityWasCreated",
    "EntityWasUpdated",
    "EntityWasDeleted",
    "AppointmentWasCreated",
  ]),
  model: z.enum(["Patient", "Appointment", "Invoice", "Payment"]),
  id: z.union([z.number(), z.string()]).transform((val) => Number(val)),
  payload: z.record(z.unknown()), // Will be validated by specific payload schemas
});

// AutoPatient webhook event schema (for bidirectional sync)
export const APWebhookEventSchema = z.object({
  calendar: z.object({
    id: z.string(),
    appointmentId: z.string(),
    startTime: z.string(),
    endTime: z.string(),
    status: z.string(),
    created_by_meta: z.object({
// CliniCore webhook event structure (same as legacy Socket.io events)
export interface CCWebhookEvent {
  event:
    | "EntityWasCreated"
    | "EntityWasUpdated"
    | "EntityWasDeleted"
    | "AppointmentWasCreated";
  model: "Patient" | "Appointment" | "Invoice" | "Payment";
  id: number;
  payload:
    | CCPatientPayload
    | CCAppointmentPayload
    | CCInvoicePayload
    | CCPaymentPayload;
}

// Exact payload types based on legacy Socket.io data
export interface CCPatientPayload {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  firstName: string;
  lastName: string;
  dob: string | null;
  ssn: string | null;
  flashMessage: string;
  active: boolean;
  phoneMobile: string;
  email: string;
  title: string | null;
  titleSuffix: string | null;
  healthInsurance: string | null;
  gender: string | null;
  addresses: any[];
  categories: number[];
  customFields: number[];
  invoices: number[];
  payments: number[];
  files: number[];
  history: number[];
  appointments: number[];
  messages: any[];
  medications: any[];
  qrUrl: string;
  avatarUrl: string | null;
}

export interface CCAppointmentPayload {
  id: number;
  uuid: string | null;
  startsAt: string;
  endsAt: string;
  arrivedAt: string | null;
  processedAt: string | null;
  treatmentStartedAt: string | null;
  allDay: boolean;
  slot: boolean;
  subject: string | null;
  title: string;
  firstOfPatient: boolean;
  onPatientBirthday: boolean;
  description: string;
  color: string | null;
  patientCategories: number[];
  patientsPreview: any[];
  patients: number[];
  people: number[];
  resources: number[];
  categories: number[];
  location?: number;
  services: number[];
  series: number;
  canceledWhy: string | null;
  createdAt: string;
  updatedAt: string;
  canceledAt: string | null;
  createdBy: number;
  updatedBy: number;
  canceledBy: number | null;
  reminderAt: string | null;
  reminderStatus: string;
  reminderSentAt: string | null;
  deletedAt: string | null;
}

export interface CCInvoicePayload {
  id: number;
  invoiceNumber?: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
  addressText?: null;
  discount?: number;
  status?: string;
  vatId?: null;
  description?: string;
  note?: null;
  canceledWhy?: null;
  settlementStatus?: null;
  pdfUrl?: string;
  invoiceNumberSequence?: string;
  address?: {
    id?: number;
    label?: null;
    name?: string;
    street?: null;
    streetNumber?: null;
    postalCode?: null;
    city?: null;
    country?: string;
    primary?: number;
  };
  appointment?: null;
  positions?: {
    id?: number;
    name?: string;
    gross?: number;
    discount?: number;
    [key: string]: any;
  }[];
  reversedBy?: null;
  reverses?: null;
  patient?: number;
  payments?: {
    id?: number;
    [key: string]: any;
  }[];
  practicioner?: number;
  settlement?: null;
  sentAt?: null;
  wahonlinedAt?: null;
  diagnoses?: {
    id?: number;
    [key: string]: any;
  }[];
}

export interface CCPaymentPayload {
  id: number;
  paymentNumber: string;
  gross: number;
  customIdentification: null;
  comment: string;
  createdAt: string;
  date: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  register: number;
  patient: number;
  invoicePayments: {
    id: number;
    gross: number;
    invoice: number;
    payment: number;
  }[];
  reversedBy: null;
  reverses: null;
  pdfUrl: string;
  canceled: boolean;
}

// AutoPatient webhook structure (for bidirectional sync)
export interface APWebhookEvent {
  calendar: {
    id: string;
    appointmentId: string;
    startTime: string;
    endTime: string;
    status: string;
    created_by_meta: {
      source: string;
      channel?: string;
    };
    last_updated_by_meta?: {
      source: string;
      channel?: string;
    };
  };
  contact_id: string;
  email?: string;
  phone?: string;
}

export interface WebhookContext {
  event: CCWebhookEvent;
  requestId: string;
}

export interface WebhookResponse {
  success: boolean;
  message: string;
  requestId: string;
  processedAt: string;
  data?: Record<string, unknown>;
}
```
### 3. Create CliniCore Webhook Processor

Create `src/webhooks/ccProcessor.ts`:
```typescript
import type { Context } from "hono";
import type { CCWebhookEvent, WebhookResponse } from "@/types/webhooks";
import { logger } from "@utils";
import { createError } from "@utils";
import { ccEventRouter } from "./ccEventRouter";

export const ccWebhookProcessor = async (c: Context): Promise<Response> => {
  const requestId = crypto.randomUUID();

  try {
    const body = (await c.req.json()) as CCWebhookEvent;

    logger.info("CliniCore webhook received", {
      requestId,
      event: body.event,
      model: body.model,
      id: body.id,
      payloadKeys: Object.keys(body.payload || {}),
    });

    // Validate required fields
    if (!body.event || !body.model || !body.payload || !body.id) {
      throw createError(
        "Missing required fields: event, model, payload, or id",
        400
      );
    }

    // Process the event directly (no job queue)
    const result = await ccEventRouter.processEvent(body, requestId);

    const response: WebhookResponse = {
      success: result.success,
      message: result.message,
      requestId,
      processedAt: new Date().toISOString(),
      data: result.data,
    };

    return c.json(response, result.success ? 200 : 500);
  } catch (error) {
    logger.error("CliniCore webhook processing failed", {
      requestId,
      error: error instanceof Error ? error.message : String(error),
    });

    const response: WebhookResponse = {
      success: false,
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
      requestId,
      processedAt: new Date().toISOString(),
    };

    const statusCode =
      error instanceof Error && "status" in error
        ? (error as { status: number }).status
        : 500;
    return c.json(response, statusCode);
  }
};
```
### 4. Update Main Index File

Update `src/index.ts` to include bidirectional webhook routing:
```typescript
import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { ccWebhookProcessor } from "./webhooks/ccProcessor";
import { apWebhookProcessor } from "./webhooks/apProcessor";
import { webhookValidator } from "./middlewares/webhookValidator";

const app = new Hono<Env>();
app.use(contextStorage());

app.onError((err, c) => {
  console.error(err);
  return c.json(
    {
      message: "Internal Server Error",
      requestId: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
    },
    500
  );
});

// CliniCore webhook endpoint (replaces Socket.io)
app.post("/webhooks/cc", webhookValidator, ccWebhookProcessor);

// AutoPatient webhook endpoints (bidirectional sync)
app.post(
  "/webhooks/ap/appointment/creates",
  webhookValidator,
  apWebhookProcessor
);
app.post(
  "/webhooks/ap/appointment/updates",
  webhookValidator,
  apWebhookProcessor
);

// Health check
app.get("/", (c) => c.text("DermaCare Data Sync Service - OK"));
app.get("/health", (c) =>
  c.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "2.0.0",
  })
);

export default app;
```
## Dependencies

- **Prerequisite**: None (foundational task)
- **Blocks**: Task 2 (CliniCore Event Router), Task 5 (Webhook Validation Middleware)

## Testing Strategy
```typescript
// Test file: src/webhooks/__tests__/routing.test.ts
describe("CliniCore Webhook Routing", () => {
  test("should accept valid CC webhook", async () => {
    const response = await app.request("/webhooks/cc", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        type: "patient",
        id: 123,
        payload: {
          id: 123,
          firstName: "John",
          lastName: "Doe",
          email: "<EMAIL>",
          phoneMobile: "+**********",
          updatedAt: "2024-01-01T10:00:00Z",
        },
any;
  }[];
}

export interface CCPaymentPayload {
  id: number;
  paymentNumber: string;
  gross: number;
  customIdentification: null;
  comment: string;
  createdAt: string;
  date: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  register: number;
  patient: number;
  invoicePayments: {
    id: number;
    gross: number;
    invoice: number;
    payment: number;
  }[];
  reversedBy: null;
  reverses: null;
  pdfUrl: string;
  canceled: boolean;
}

// AutoPatient webhook structure (for bidirectional sync)
export interface APWebhookEvent {
  calendar: {
    id: string;
    appointmentId: string;
    startTime: string;
    endTime: string;
    status: string;
    created_by_meta: {
      source: string;
      channel?: string;
    };
    last_updated_by_meta?: {
      source: string;
      channel?: string;
    };
  };
  contact_id: string;
  email?: string;
  phone?: string;
}

export interface WebhookContext {
  event: CCWebhookEvent;
  requestId: string;
}

export interface WebhookResponse {
  success: boolean;
  message: string;
  requestId: string;
  processedAt: string;
  data?: Record<string, unknown>;
}
```
### 3. Create CliniCore Webhook Processor

Create `src/webhooks/ccProcessor.ts`:
```typescript
import type { Context } from "hono";
import type { CCWebhookEvent, WebhookResponse } from "@/types/webhooks";
import { logger } from "@utils";
import { createError } from "@utils";
import { ccEventRouter } from "./ccEventRouter";

export const ccWebhookProcessor = async (c: Context): Promise<Response> => {
  const requestId = crypto.randomUUID();

  try {
    const body = (await c.req.json()) as CCWebhookEvent;

    logger.info("CliniCore webhook received", {
      requestId,
      type: body.type,
      id: body.id,
      payloadKeys: Object.keys(body.payload || {}),
    });

    // Validate required fields
    if (!body.type || !body.payload || !body.id) {
      throw createError("Missing required fields: type, payload, or id", 400);
    }

    // Process the event directly (no job queue)
    const result = await ccEventRouter.processEvent(body, requestId);

    const response: WebhookResponse = {
      success: result.success,
      message: result.message,
      requestId,
      processedAt: new Date().toISOString(),
      data: result.data,
    };

    return c.json(response, result.success ? 200 : 500);
  } catch (error) {
    logger.error("CliniCore webhook processing failed", {
      requestId,
      error: error instanceof Error ? error.message : String(error),
    });

    const response: WebhookResponse = {
      success: false,
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
      requestId,
      processedAt: new Date().toISOString(),
    };

    const statusCode =
      error instanceof Error && "status" in error
        ? (error as { status: number }).status
        : 500;
    return c.json(response, statusCode);
  }
};
```
### 4. Update Main Index File

Update `src/index.ts` to include bidirectional webhook routing:
```typescript
import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { ccWebhookProcessor } from "./webhooks/ccProcessor";
import { apWebhookProcessor } from "./webhooks/apProcessor";
import { webhookValidator } from "./middlewares/webhookValidator";

const app = new Hono<Env>();
app.use(contextStorage());

app.onError((err, c) => {
  console.error(err);
  return c.json(
    {
      message: "Internal Server Error",
      requestId: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
    },
    500
  );
});

// CliniCore webhook endpoint (replaces Socket.io)
app.post("/webhooks/cc", webhookValidator, ccWebhookProcessor);

// AutoPatient webhook endpoints (bidirectional sync)
app.post(
  "/webhooks/ap/appointment/creates",
  webhookValidator,
  apWebhookProcessor
);
app.post(
  "/webhooks/ap/appointment/updates",
  webhookValidator,
  apWebhookProcessor
);

// Health check
app.get("/", (c) => c.text("DermaCare Data Sync Service - OK"));
app.get("/health", (c) =>
  c.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "2.0.0",
  })
);

export default app;
```
## Dependencies

- **Prerequisite**: None (foundational task)
- **Blocks**: Task 2 (CliniCore Event Router), Task 5 (Webhook Validation Middleware)

## Testing Strategy
```typescript
// Test file: src/webhooks/__tests__/routing.test.ts
describe("CliniCore Webhook Routing", () => {
  test("should accept valid CC webhook", async () => {
    const response = await app.request("/webhooks/cc", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        event: "EntityWasCreated",
        model: "Patient",

          429
        );
      }

      // Record this request
      await recordRequest(kv, key, now, config.windowMs);

      await next();
    } catch (error) {
      logger.error("Rate limiting error", {
        requestId,
        error: error instanceof Error ? error.message : String(error),
      });

      // Continue on rate limiting errors
      await next();
    }
  };
};

async function getCurrentRequestCount(
  kv: KVNamespace,
  key: string,
  windowStart: number
): Promise<number> {
  const value = await kv.get(`${key}:count`);
  if (!value) return 0;

  const data = JSON.parse(value) as { count: number; firstRequest: number };

  // If the window has expired, reset count
  if (data.firstRequest < windowStart) {
    return 0;
  }

  return data.count;
}

async function recordRequest(
  kv: KVNamespace,
  key: string,
  now: number,
  windowMs: number
): Promise<void> {
  const countKey = `${key}:count`;
  const existing = await kv.get(countKey);

  let count = 1;
  let firstRequest = now;

  if (existing) {
    const data = JSON.parse(existing) as {
      count: number;
      firstRequest: number;
    };
    const windowStart = now - windowMs;

    if (data.firstRequest >= windowStart) {
      count = data.count + 1;
      firstRequest = data.firstRequest;
    }
  }

  await kv.put(countKey, JSON.stringify({ count, firstRequest }), {
    expirationTtl: Math.ceil(windowMs / 1000) + 60, // Add buffer
  });
}

// Default rate limiter for webhooks
export const webhookRateLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 100, // 100 requests per minute per IP
  keyGenerator: (c) => {
    const ip = c.req.header("cf-connecting-ip") || "unknown";
    return `webhook_rate_limit:${ip}`;
  },
});
```

### 4. Update Main Application

Update `src/index.ts` to include validation middleware:

```typescript
import { webhookValidator } from "./middlewares/webhookValidator";
import { webhookRateLimiter } from "./middlewares/rateLimiter";

// Apply middleware to CliniCore webhook route
app.post(
  "/webhooks/cc",
  webhookRateLimiter,
  webhookValidator,
  ccWebhookProcessor
);
```

## Dependencies

- **Prerequisite**: Task 1 (Webhook Routing Infrastructure)
- **Integrates with**: Task 2 (Webhook Event Processor)

## Testing Strategy

```typescript
// Test file: src/middlewares/__tests__/webhookValidator.test.ts
describe("Webhook Validator", () => {
  test("should validate correct patient webhook", async () => {
    const validPayload = {
      event: "EntityWasCreated",
      model: "Patient",
      id: 123,
      payload: {
        id: 123,
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        updatedAt: "2023-01-01T00:00:00Z",
        createdAt: "2023-01-01T00:00:00Z",
      },
    };

    // Test validation passes
  });

  test("should reject invalid payload", async () => {
    const invalidPayload = {
      event: "InvalidEvent",
      model: "Patient",
      id: "invalid",
    };

    // Test validation fails with proper error
  });
});
```

## Implementation Notes

- Use Zod for runtime type validation and TypeScript integration
- Implement comprehensive error messages for debugging
- Add rate limiting to prevent abuse
- Follow security best practices for webhook validation
- Ensure all validation errors are properly logged
