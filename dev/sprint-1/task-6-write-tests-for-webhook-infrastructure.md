# Task 6: Write Tests for Webhook Infrastructure

## Description

Create comprehensive tests for webhook routing and event processing. This ensures the foundation infrastructure works correctly before building direct webhook handlers on top of it.

## Acceptance Criteria

- [ ] Unit tests for webhook routing and validation
- [ ] Integration tests for complete webhook flow
- [ ] Error handling test scenarios
- [ ] Rate limiting and security tests
- [ ] Never use `any` or `as any` - use proper TypeScript types
- [ ] Achieve >90% code coverage for webhook infrastructure

## Technical Implementation Details

### 1. Set Up Test Environment

Create `src/webhooks/__tests__/setup.ts`:

```typescript
import { Hono } from "hono";
import { webhookValidator } from "@/middlewares/webhookValidator";
import { webhookRateLimiter } from "@/middlewares/rateLimiter";
import { ccWebhookProcessor } from "@/webhooks/ccProcessor";

// Create test app instance
export const createTestApp = () => {
  const app = new Hono<Env>();

  app.post(
    "/webhooks/cc",
    webhookRateLimiter,
    webhookValidator,
    ccWebhookProcessor
  );

  return app;
};

// Mock KV for testing
export const mockKV = {
  get: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  list: jest.fn(),
};

// Mock environment
export const mockEnv: Env = {
  kv: mockKV as unknown as KVNamespace,
};

// Test data factories
export const createValidPatientWebhook = (overrides = {}) => ({
  event: "EntityWasCreated" as const,
  model: "Patient" as const,
  id: 123,
  payload: {
    id: 123,
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phoneMobile: "+**********",
    updatedAt: "2023-01-01T00:00:00Z",
    createdAt: "2023-01-01T00:00:00Z",
    ...overrides,
  },
});

export const createValidAppointmentWebhook = (overrides = {}) => ({
  event: "AppointmentWasCreated" as const,
  model: "Appointment" as const,
  id: 456,
  payload: {
    id: 456,
    title: "Test Appointment",
    startsAt: "2023-01-01T10:00:00Z",
    endsAt: "2023-01-01T11:00:00Z",
    patients: [123],
    updatedAt: "2023-01-01T00:00:00Z",
    createdAt: "2023-01-01T00:00:00Z",
    ...overrides,
  },
});
```

### 2. Webhook Routing Tests

Create `src/webhooks/__tests__/routing.test.ts`:

```typescript
import { createTestApp, createValidPatientWebhook, mockKV } from "./setup";

describe("Webhook Routing", () => {
  let app: ReturnType<typeof createTestApp>;

  beforeEach(() => {
    app = createTestApp();
    jest.clearAllMocks();

    // Mock KV responses
    mockKV.get.mockResolvedValue(null);
    mockKV.put.mockResolvedValue(undefined);
  });

  describe("Valid Requests", () => {
    test("should accept valid patient creation webhook", async () => {
      const webhook = createValidPatientWebhook();

      const response = await app.request("/webhooks/cc", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(webhook),
      });

      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.requestId).toBeDefined();
      expect(data.processedAt).toBeDefined();
    });

    test("should accept valid appointment creation webhook", async () => {
      const webhook = createValidAppointmentWebhook();

      const response = await app.request("/webhooks/cc", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(webhook),
      });

      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data.success).toBe(true);
    });
  });

  describe("Invalid Requests", () => {
    test("should reject non-JSON content type", async () => {
      const webhook = createValidPatientWebhook();

      const response = await app.request("/webhooks/cc", {
        method: "POST",
        headers: { "Content-Type": "text/plain" },
        body: JSON.stringify(webhook),
      });

      expect(response.status).toBe(400);

      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.message).toContain("Content-Type");
    });

    test("should reject invalid JSON", async () => {
      const response = await app.request("/webhooks/cc", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: "invalid json",
      });

      expect(response.status).toBe(400);

      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.message).toContain("Invalid JSON");
    });

    test("should reject oversized requests", async () => {
      const largePayload = {
        ...createValidPatientWebhook(),
        payload: {
          ...createValidPatientWebhook().payload,
          largeData: "x".repeat(11 * 1024 * 1024), // 11MB
        },
      };

      const response = await app.request("/webhooks/cc/test-location", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Content-Length": String(JSON.stringify(largePayload).length),
        },
        body: JSON.stringify(largePayload),
      });

      expect(response.status).toBe(413);
    });
  });
});
```

### 3. Webhook Validation Tests

Create `src/middlewares/__tests__/webhookValidator.test.ts`:

```typescript
import { webhookValidator } from "@/middlewares/webhookValidator";
import {
  createValidPatientWebhook,
  createValidAppointmentWebhook,
} from "../webhooks/__tests__/setup";

describe("Webhook Validator", () => {
  let mockContext: any;
  let mockNext: jest.Mock;

  beforeEach(() => {
    mockNext = jest.fn();
    mockContext = {
      req: {
        param: jest.fn(),
        header: jest.fn(),
        json: jest.fn(),
      },
      set: jest.fn(),
      json: jest.fn(),
    };
  });

  describe("Payload Validation", () => {
    test("should validate patient payload correctly", async () => {
      const webhook = createValidPatientWebhook();

      mockContext.req.param.mockReturnValue("test-location");
      mockContext.req.header.mockImplementation((name: string) => {
        switch (name) {
          case "content-type":
            return "application/json";
          case "content-length":
            return "1000";
          default:
            return undefined;
        }
      });
      mockContext.req.json.mockResolvedValue(webhook);

      await webhookValidator(mockContext, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockContext.set).toHaveBeenCalledWith(
        "validatedWebhook",
        expect.objectContaining({
          event: "EntityWasCreated",
          model: "Patient",
          id: 123,
        })
      );
    });

    test("should validate appointment payload correctly", async () => {
      const webhook = createValidAppointmentWebhook();

      mockContext.req.param.mockReturnValue("test-location");
      mockContext.req.header.mockImplementation((name: string) => {
        switch (name) {
          case "content-type":
            return "application/json";
          case "content-length":
            return "1000";
          default:
            return undefined;
        }
      });
      mockContext.req.json.mockResolvedValue(webhook);

      await webhookValidator(mockContext, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    test("should reject invalid patient payload", async () => {
      const invalidWebhook = {
        event: "EntityWasCreated",
        model: "Patient",
        id: 123,
        payload: {
          id: 123,
          // Missing required firstName and lastName
          email: "invalid-email", // Invalid email format
        },
      };

      mockContext.req.param.mockReturnValue("test-location");
      mockContext.req.header.mockImplementation((name: string) => {
        switch (name) {
          case "content-type":
            return "application/json";
          case "content-length":
            return "1000";
          default:
            return undefined;
        }
      });
      mockContext.req.json.mockResolvedValue(invalidWebhook);

      await webhookValidator(mockContext, mockNext);

      expect(mockNext).not.toHaveBeenCalled();
      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: "Validation failed",
          errors: expect.arrayContaining([
            expect.objectContaining({
              path: expect.stringContaining("firstName"),
            }),
          ]),
        }),
        400
      );
    });

    test("should reject unknown event types", async () => {
      const invalidWebhook = {
        event: "UnknownEvent",
        model: "Patient",
        id: 123,
        payload: createValidPatientWebhook().payload,
      };

      mockContext.req.param.mockReturnValue("test-location");
      mockContext.req.header.mockImplementation((name: string) => {
        switch (name) {
          case "content-type":
            return "application/json";
          case "content-length":
            return "1000";
          default:
            return undefined;
        }
      });
      mockContext.req.json.mockResolvedValue(invalidWebhook);

      await webhookValidator(mockContext, mockNext);

      expect(mockNext).not.toHaveBeenCalled();
      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          errors: expect.arrayContaining([
            expect.objectContaining({
              path: "event",
            }),
          ]),
        }),
        400
      );
    });
  });
});
```

### 4. Rate Limiting Tests

Create `src/middlewares/__tests__/rateLimiter.test.ts`:

```typescript
import { createRateLimiter } from "@/middlewares/rateLimiter";
import { mockKV } from "../webhooks/__tests__/setup";

describe("Rate Limiter", () => {
  let rateLimiter: ReturnType<typeof createRateLimiter>;
  let mockContext: any;
  let mockNext: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    mockNext = jest.fn();

    rateLimiter = createRateLimiter({
      windowMs: 60000, // 1 minute
      maxRequests: 5,
    });

    mockContext = {
      req: {
        header: jest.fn().mockReturnValue("127.0.0.1"),
      },
      get: jest.fn().mockReturnValue("test-request-id"),
      json: jest.fn(),
    };
  });

  test("should allow requests under limit", async () => {
    mockKV.get.mockResolvedValue(null); // No existing requests
    mockKV.put.mockResolvedValue(undefined);

    await rateLimiter(mockContext, mockNext);

    expect(mockNext).toHaveBeenCalled();
    expect(mockKV.put).toHaveBeenCalled();
  });

  test("should block requests over limit", async () => {
    const existingData = JSON.stringify({
      count: 5,
      firstRequest: Date.now() - 30000, // 30 seconds ago
    });

    mockKV.get.mockResolvedValue(existingData);

    await rateLimiter(mockContext, mockNext);

    expect(mockNext).not.toHaveBeenCalled();
    expect(mockContext.json).toHaveBeenCalledWith(
      expect.objectContaining({
        success: false,
        message: "Rate limit exceeded",
      }),
      429
    );
  });

  test("should reset count after window expires", async () => {
    const expiredData = JSON.stringify({
      count: 10,
      firstRequest: Date.now() - 120000, // 2 minutes ago (expired)
    });

    mockKV.get.mockResolvedValue(expiredData);
    mockKV.put.mockResolvedValue(undefined);

    await rateLimiter(mockContext, mockNext);

    expect(mockNext).toHaveBeenCalled();
  });
});
```

### 5. Integration Tests

Create `src/webhooks/__tests__/integration.test.ts`:

```typescript
import { createTestApp, createValidPatientWebhook, mockKV } from "./setup";

describe("Webhook Integration", () => {
  let app: ReturnType<typeof createTestApp>;

  beforeEach(() => {
    app = createTestApp();
    jest.clearAllMocks();

    // Mock successful KV operations
    mockKV.get.mockResolvedValue(null);
    mockKV.put.mockResolvedValue(undefined);
  });

  test("should process complete webhook flow", async () => {
    const webhook = createValidPatientWebhook();

    const response = await app.request("/webhooks/cc/test-clinic", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "User-Agent": "CC-Webhook/1.0",
      },
      body: JSON.stringify(webhook),
    });

    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data).toMatchObject({
      success: true,
      message: expect.stringContaining("processed"),
      requestId: expect.any(String),
      processedAt: expect.any(String),
    });

    // Verify skip logic was called
    expect(mockKV.put).toHaveBeenCalled();
  });

  test("should handle multiple concurrent requests", async () => {
    const webhook = createValidPatientWebhook();

    const requests = Array.from({ length: 3 }, (_, i) =>
      app.request(`/webhooks/cc/clinic-${i}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...webhook,
          id: webhook.id + i,
        }),
      })
    );

    const responses = await Promise.all(requests);

    responses.forEach((response) => {
      expect(response.status).toBe(200);
    });
  });
});
```

## Dependencies

- **Prerequisite**: Tasks 1-5 (All Sprint 1 infrastructure tasks)
- **Blocks**: Sprint 2 development (ensures foundation is solid)

## Testing Strategy

- Unit tests for individual components
- Integration tests for complete flows
- Error scenario testing
- Performance and concurrency testing
- Mock external dependencies (KV, databases)

## Implementation Notes

- Use Jest for testing framework
- Mock all external dependencies
- Test both success and failure scenarios
- Ensure tests are deterministic and fast
- Follow existing project testing patterns
- Achieve high code coverage for confidence in foundation
