# Task 1: Comprehensive Integration Testing

## Description
Create end-to-end tests covering all sync scenarios and edge cases. This ensures the complete migration is working correctly and all business logic has been properly ported from the legacy system.

## Acceptance Criteria
- [ ] End-to-end test suite covering all sync scenarios
- [ ] Integration tests for bidirectional data flow
- [ ] Edge case and error scenario testing
- [ ] Performance and load testing
- [ ] Never use `any` or `as any` - use proper TypeScript types
- [ ] Test coverage >95% for critical sync paths

## Technical Implementation Details

### 1. Create Test Infrastructure
Create `src/__tests__/integration/setup.ts`:

```typescript
import { Hono } from 'hono'
import { testClient } from 'hono/testing'
import app from '@/index'

// Test environment setup
export const setupTestEnvironment = () => {
  // Mock external APIs
  jest.mock('@libAP', () => ({
    apContactRequest: {
      upsert: jest.fn(),
      update: jest.fn(),
      get: jest.fn()
    },
    apAppointmentRequest: {
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      getContact: jest.fn()
    }
  }))

  jest.mock('@libCC', () => ({
    ccPatientRequest: {
      searchByEmail: jest.fn(),
      searchByPhone: jest.fn(),
      getPatient: jest.fn(),
      createPatient: jest.fn(),
      updatePatient: jest.fn(),
      getPatientInvoices: jest.fn()
    },
    ccAppointmentRequest: {
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn()
    },
    ccInvoiceRequest: {
      getPatientInvoices: jest.fn()
    }
  }))

  // Mock database
  jest.mock('@database', () => ({
    getDb: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      returning: jest.fn()
    }))
  }))

  // Mock KV storage
  const mockKV = {
    get: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    list: jest.fn()
  }

  return {
    app: testClient(app),
    mockKV,
    mocks: {
      apContactRequest: require('@libAP').apContactRequest,
      apAppointmentRequest: require('@libAP').apAppointmentRequest,
      ccPatientRequest: require('@libCC').ccPatientRequest,
      ccAppointmentRequest: require('@libCC').ccAppointmentRequest,
      db: require('@database').getDb()
    }
  }
}

// Test data factories
export const createTestPatientData = (overrides = {}) => ({
  id: 123,
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phoneMobile: '+**********',
  dob: '1990-01-01',
  gender: 'male',
  customFields: [1, 2],
  appointments: [456],
  invoices: [789],
  payments: [101],
  updatedAt: '2023-01-01T00:00:00Z',
  createdAt: '2023-01-01T00:00:00Z',
  ...overrides
})

export const createTestAppointmentData = (overrides = {}) => ({
  id: 456,
  title: 'Test Appointment',
  startsAt: '2023-01-01T10:00:00Z',
  endsAt: '2023-01-01T11:00:00Z',
  patients: [123],
  services: [1],
  canceledAt: null,
  canceledWhy: null,
  firstOfPatient: false,
  updatedAt: '2023-01-01T00:00:00Z',
  createdAt: '2023-01-01T00:00:00Z',
  ...overrides
})

export const createTestInvoiceData = (overrides = {}) => ({
  id: 789,
  patient: 123,
  pdfUrl: 'https://example.com/invoice.pdf',
  discount: 10,
  status: 'paid',
  positions: [
    { name: 'Consultation', gross: 100 },
    { name: 'Treatment', gross: 200, discount: 20 }
  ],
  diagnoses: [
    { text: 'Test diagnosis' }
  ],
  practicioner: 1,
  updatedAt: '2023-01-01T00:00:00Z',
  createdAt: '2023-01-01T00:00:00Z',
  ...overrides
})
```

### 2. Create End-to-End Test Suites
Create `src/__tests__/integration/patientSync.test.ts`:

```typescript
import { setupTestEnvironment, createTestPatientData } from './setup'

describe('Patient Sync Integration', () => {
  let testEnv: ReturnType<typeof setupTestEnvironment>

  beforeEach(() => {
    testEnv = setupTestEnvironment()
    jest.clearAllMocks()
  })

  describe('CC to AP Patient Sync', () => {
    test('should create new patient in AP when CC patient is created', async () => {
      const patientData = createTestPatientData()
      
      // Mock AP contact creation
      testEnv.mocks.apContactRequest.upsert.mockResolvedValue({
        id: 'ap-contact-123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      })

      // Mock database operations
      testEnv.mocks.db.returning.mockResolvedValue([{
        id: 'patient-record-123',
        apId: 'ap-contact-123',
        ccId: 123
      }])

      const response = await testEnv.app.webhooks.cc['test-clinic'].$post({
        json: {
          event: 'EntityWasCreated',
          model: 'Patient',
          id: 123,
          payload: patientData
        }
      })

      expect(response.status).toBe(200)
      const result = await response.json()
      
      expect(result.success).toBe(true)
      expect(testEnv.mocks.apContactRequest.upsert).toHaveBeenCalledWith(
        expect.objectContaining({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '+**********'
        }),
        expect.any(String)
      )
    })

    test('should update existing patient in AP when CC patient is updated', async () => {
      const patientData = createTestPatientData({
        firstName: 'Jane',
        email: '<EMAIL>'
      })

      // Mock existing patient found
      testEnv.mocks.db.returning.mockResolvedValueOnce([{
        id: 'patient-record-123',
        apId: 'ap-contact-123',
        ccId: 123,
        email: '<EMAIL>',
        ccData: createTestPatientData()
      }])

      // Mock AP contact update
      testEnv.mocks.apContactRequest.update.mockResolvedValue({
        id: 'ap-contact-123',
        firstName: 'Jane',
        email: '<EMAIL>'
      })

      const response = await testEnv.app.webhooks.cc['test-clinic'].$post({
        json: {
          event: 'EntityWasUpdated',
          model: 'Patient',
          id: 123,
          payload: patientData
        }
      })

      expect(response.status).toBe(200)
      const result = await response.json()
      
      expect(result.success).toBe(true)
      expect(testEnv.mocks.apContactRequest.update).toHaveBeenCalledWith(
        'ap-contact-123',
        expect.objectContaining({
          firstName: 'Jane',
          email: '<EMAIL>'
        })
      )
    })

    test('should skip update when no significant changes detected', async () => {
      const patientData = createTestPatientData()

      // Mock existing patient with same data
      testEnv.mocks.db.returning.mockResolvedValueOnce([{
        id: 'patient-record-123',
        apId: 'ap-contact-123',
        ccId: 123,
        email: '<EMAIL>',
        ccData: patientData
      }])

      const response = await testEnv.app.webhooks.cc['test-clinic'].$post({
        json: {
          event: 'EntityWasUpdated',
          model: 'Patient',
          id: 123,
          payload: patientData
        }
      })

      expect(response.status).toBe(200)
      const result = await response.json()
      
      expect(result.success).toBe(true)
      expect(result.data?.action).toBe('skipped')
      expect(testEnv.mocks.apContactRequest.update).not.toHaveBeenCalled()
    })
  })

  describe('AP to CC Patient Sync', () => {
    test('should create patient in CC when AP appointment is created', async () => {
      const apWebhookData = {
        calendar: {
          id: 'cal-123',
          appointmentId: 'apt-456',
          startTime: '2023-01-01T10:00:00Z',
          endTime: '2023-01-01T11:00:00Z',
          status: 'scheduled',
          created_by_meta: {
            source: 'manual'
          }
        },
        contact_id: 'ap-contact-123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe'
      }

      // Mock AP contact retrieval
      testEnv.mocks.apAppointmentRequest.getContact.mockResolvedValue({
        id: 'ap-contact-123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      })

      // Mock CC patient creation
      testEnv.mocks.ccPatientRequest.createPatient.mockResolvedValue({
        id: 124,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      })

      // Mock CC appointment creation
      testEnv.mocks.ccAppointmentRequest.create.mockResolvedValue({
        id: 457,
        title: 'AP Appointment',
        startsAt: '2023-01-01T10:00:00Z',
        patients: [124]
      })

      const response = await testEnv.app.webhooks.ap['test-clinic'].appointment.create.$post({
        json: apWebhookData
      })

      expect(response.status).toBe(200)
      const result = await response.json()
      
      expect(result.success).toBe(true)
      expect(testEnv.mocks.ccPatientRequest.createPatient).toHaveBeenCalled()
      expect(testEnv.mocks.ccAppointmentRequest.create).toHaveBeenCalled()
    })
  })
})
```

### 3. Create Bidirectional Sync Tests
Create `src/__tests__/integration/bidirectionalSync.test.ts`:

```typescript
import { setupTestEnvironment, createTestPatientData, createTestAppointmentData } from './setup'

describe('Bidirectional Sync Integration', () => {
  let testEnv: ReturnType<typeof setupTestEnvironment>

  beforeEach(() => {
    testEnv = setupTestEnvironment()
    jest.clearAllMocks()
  })

  test('should prevent sync loops between CC and AP', async () => {
    // Test CC -> AP -> CC loop prevention
    const patientData = createTestPatientData()

    // Mock AP contact creation with CC source
    testEnv.mocks.apContactRequest.upsert.mockResolvedValue({
      id: 'ap-contact-123',
      source: 'cc',
      tags: ['cc_api']
    })

    // First sync: CC to AP
    const ccResponse = await testEnv.app.webhooks.cc['test-clinic'].$post({
      json: {
        event: 'EntityWasCreated',
        model: 'Patient',
        id: 123,
        payload: patientData
      }
    })

    expect(ccResponse.status).toBe(200)

    // Simulate AP webhook that would be triggered by CC sync
    const apWebhookData = {
      calendar: {
        id: 'cal-123',
        appointmentId: 'apt-456',
        startTime: '2023-01-01T10:00:00Z',
        endTime: '2023-01-01T11:00:00Z',
        status: 'scheduled',
        created_by_meta: {
          source: 'cc_api' // This should be detected as CC-originated
        }
      },
      contact_id: 'ap-contact-123'
    }

    // Second sync: AP to CC (should be skipped)
    const apResponse = await testEnv.app.webhooks.ap['test-clinic'].appointment.create.$post({
      json: apWebhookData
    })

    expect(apResponse.status).toBe(200)
    const apResult = await apResponse.json()
    
    expect(apResult.success).toBe(true)
    expect(apResult.data?.action).toBe('skipped')
    expect(testEnv.mocks.ccAppointmentRequest.create).not.toHaveBeenCalled()
  })

  test('should handle concurrent sync operations', async () => {
    const patientData = createTestPatientData()

    // Mock successful operations
    testEnv.mocks.apContactRequest.upsert.mockResolvedValue({
      id: 'ap-contact-123'
    })

    // Simulate concurrent webhook processing
    const promises = Array.from({ length: 5 }, (_, i) => 
      testEnv.app.webhooks.cc['test-clinic'].$post({
        json: {
          event: 'EntityWasCreated',
          model: 'Patient',
          id: 123 + i,
          payload: { ...patientData, id: 123 + i }
        }
      })
    )

    const responses = await Promise.all(promises)

    // All should succeed
    responses.forEach(response => {
      expect(response.status).toBe(200)
    })

    // Verify all contacts were created
    expect(testEnv.mocks.apContactRequest.upsert).toHaveBeenCalledTimes(5)
  })
})
```

### 4. Create Error Scenario Tests
Create `src/__tests__/integration/errorScenarios.test.ts`:

```typescript
import { setupTestEnvironment, createTestPatientData } from './setup'

describe('Error Scenario Integration', () => {
  let testEnv: ReturnType<typeof setupTestEnvironment>

  beforeEach(() => {
    testEnv = setupTestEnvironment()
    jest.clearAllMocks()
  })

  test('should handle AP API failures gracefully', async () => {
    const patientData = createTestPatientData()

    // Mock AP API failure
    testEnv.mocks.apContactRequest.upsert.mockRejectedValue(
      new Error('AP API rate limit exceeded')
    )

    const response = await testEnv.app.webhooks.cc['test-clinic'].$post({
      json: {
        event: 'EntityWasCreated',
        model: 'Patient',
        id: 123,
        payload: patientData
      }
    })

    expect(response.status).toBe(500)
    const result = await response.json()
    
    expect(result.success).toBe(false)
    expect(result.message).toContain('rate limit')
  })

  test('should handle database failures gracefully', async () => {
    const patientData = createTestPatientData()

    // Mock successful AP call but database failure
    testEnv.mocks.apContactRequest.upsert.mockResolvedValue({
      id: 'ap-contact-123'
    })
    
    testEnv.mocks.db.returning.mockRejectedValue(
      new Error('Database connection failed')
    )

    const response = await testEnv.app.webhooks.cc['test-clinic'].$post({
      json: {
        event: 'EntityWasCreated',
        model: 'Patient',
        id: 123,
        payload: patientData
      }
    })

    expect(response.status).toBe(500)
    const result = await response.json()
    
    expect(result.success).toBe(false)
    expect(result.message).toContain('Database')
  })

  test('should handle malformed webhook data', async () => {
    const response = await testEnv.app.webhooks.cc['test-clinic'].$post({
      json: {
        event: 'InvalidEvent',
        model: 'Patient',
        // Missing required fields
      }
    })

    expect(response.status).toBe(400)
    const result = await response.json()
    
    expect(result.success).toBe(false)
    expect(result.message).toContain('Validation failed')
  })

  test('should handle timeout scenarios', async () => {
    const patientData = createTestPatientData()

    // Mock timeout
    testEnv.mocks.apContactRequest.upsert.mockImplementation(
      () => new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Request timeout')), 100)
      )
    )

    const response = await testEnv.app.webhooks.cc['test-clinic'].$post({
      json: {
        event: 'EntityWasCreated',
        model: 'Patient',
        id: 123,
        payload: patientData
      }
    })

    expect(response.status).toBe(500)
    const result = await response.json()
    
    expect(result.success).toBe(false)
    expect(result.message).toContain('timeout')
  })
})
```

### 5. Create Performance Tests
Create `src/__tests__/integration/performance.test.ts`:

```typescript
import { setupTestEnvironment, createTestPatientData } from './setup'

describe('Performance Integration', () => {
  let testEnv: ReturnType<typeof setupTestEnvironment>

  beforeEach(() => {
    testEnv = setupTestEnvironment()
    jest.clearAllMocks()
  })

  test('should process webhooks within acceptable time limits', async () => {
    const patientData = createTestPatientData()

    testEnv.mocks.apContactRequest.upsert.mockResolvedValue({
      id: 'ap-contact-123'
    })

    const startTime = Date.now()
    
    const response = await testEnv.app.webhooks.cc['test-clinic'].$post({
      json: {
        event: 'EntityWasCreated',
        model: 'Patient',
        id: 123,
        payload: patientData
      }
    })

    const processingTime = Date.now() - startTime

    expect(response.status).toBe(200)
    expect(processingTime).toBeLessThan(2000) // Should complete within 2 seconds
  })

  test('should handle high volume of concurrent requests', async () => {
    const patientData = createTestPatientData()

    testEnv.mocks.apContactRequest.upsert.mockResolvedValue({
      id: 'ap-contact-123'
    })

    const concurrentRequests = 50
    const startTime = Date.now()

    const promises = Array.from({ length: concurrentRequests }, (_, i) =>
      testEnv.app.webhooks.cc['test-clinic'].$post({
        json: {
          event: 'EntityWasCreated',
          model: 'Patient',
          id: 123 + i,
          payload: { ...patientData, id: 123 + i }
        }
      })
    )

    const responses = await Promise.all(promises)
    const totalTime = Date.now() - startTime

    // All requests should succeed
    responses.forEach(response => {
      expect(response.status).toBe(200)
    })

    // Should handle concurrent load efficiently
    expect(totalTime).toBeLessThan(10000) // Should complete within 10 seconds
    expect(testEnv.mocks.apContactRequest.upsert).toHaveBeenCalledTimes(concurrentRequests)
  })
})
```

## Dependencies
- **Prerequisite**: All Sprint 1-5 tasks completed
- **Integrates with**: All services, controllers, and job processors
- **Blocks**: Production deployment (Task 4)

## Testing Strategy
- End-to-end testing of complete data flows
- Integration testing of all service interactions
- Error scenario and edge case testing
- Performance and load testing
- Bidirectional sync validation
- Data consistency verification

## Implementation Notes
- Use comprehensive test data factories
- Mock all external dependencies consistently
- Test both success and failure scenarios
- Validate data transformations and business logic
- Ensure tests are deterministic and reliable
- Follow existing project testing patterns
- Achieve high test coverage for confidence in migration
