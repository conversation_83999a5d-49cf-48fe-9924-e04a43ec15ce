// Export types
export * from './CCTypes';

// Export request layer
export { ccApiClient } from './request/request';
export { ccPatientRequest } from './request/patientRequest';
export { ccAppointmentRequest } from './request/appointmentRequest';
export { ccCustomFieldRequest } from './request/customFieldRequest';
export { ccUserRequest } from './request/userRequest';
export { ccServiceRequest } from './request/serviceRequest';
export { ccResourceRequest } from './request/resourceRequest';
export { ccLocationRequest } from './request/locationRequest';
export { ccInvoiceRequest } from './request/invoiceRequest';
export { ccPaymentRequest } from './request/paymentRequest';

// Export service layer
export { ccPatientService } from './services/CCPatientService';
export { ccAppointmentService } from './services/CCAppointmentService';

// Export sync services (to be implemented)
// export { CCAppointmentToAPAppointmentService } from './services/CCAppointmentToAPAppointmentService';
// export { CCPatientToAPContactService } from './services/CCPatientToAPContactService'; 