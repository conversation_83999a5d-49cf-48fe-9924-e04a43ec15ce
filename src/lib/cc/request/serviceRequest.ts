import { ccApiClient } from './request';
import type { GetCCServiceType } from '@libCC/CCTypes';

/**
 * CC Service Request methods
 * 
 * This module contains all service-specific API request methods for the CC platform.
 * It provides a clean interface for making service-related API calls with proper
 * caching, error handling, and data transformation.
 */
export class CCServiceRequest {
    /**
     * Get all services
     * @returns Promise with services array
     */
    async getAllServices(): Promise<GetCCServiceType[]> {
        const response = await ccApiClient.get<{ services: GetCCServiceType[] }>(
            '/services',
            undefined,
            'cc:services'
        );
        return response.data.services;
    }

    /**
     * Get a service by ID
     * @param id - Service ID
     * @returns Promise with service data
     */
    async getService(id: number): Promise<GetCCServiceType> {
        const response = await ccApiClient.get<{ service: GetCCServiceType }>(
            `/services/${id}`,
            undefined,
            `cc:service:${id}`
        );
        return response.data.service;
    }

    /**
     * Get service by name
     * @param name - Service name
     * @returns Promise with service data or null
     */
    async getServiceByName(name: string): Promise<GetCCServiceType | null> {
        const services = await this.getAllServices();
        return services.find(service => service.name === name) || null;
    }

    /**
     * Get service by external name
     * @param externalName - External service name
     * @returns Promise with service data or null
     */
    async getServiceByExternalName(externalName: string): Promise<GetCCServiceType | null> {
        const services = await this.getAllServices();
        return services.find(service => service.externalName === externalName) || null;
    }

    /**
     * Get services by IDs
     * @param ids - Array of service IDs
     * @returns Promise with services array
     */
    async getServicesByIds(ids: number[]): Promise<GetCCServiceType[]> {
        const services = await this.getAllServices();
        return services.filter(service => ids.includes(service.id));
    }
}

// Export singleton instance
export const ccServiceRequest = new CCServiceRequest(); 