import { ccApiClient } from './request';
import type { GetCCLocationType } from '@libCC/CCTypes';

/**
 * CC Location Request methods
 * 
 * This module contains all location-specific API request methods for the CC platform.
 * It provides a clean interface for making location-related API calls with proper
 * caching, error handling, and data transformation.
 */
export class CCLocationRequest {
    /**
     * Get all locations
     * @returns Promise with locations array
     */
    async getAllLocations(): Promise<GetCCLocationType[]> {
        const response = await ccApiClient.get<{ locations: GetCCLocationType[] }>(
            '/locations',
            undefined,
            'cc:locations'
        );
        return response.data.locations;
    }

    /**
     * Get a location by ID
     * @param id - Location ID
     * @returns Promise with location data
     */
    async getLocation(id: number): Promise<GetCCLocationType> {
        const response = await ccApiClient.get<{ location: GetCCLocationType }>(
            `/locations/${id}`,
            undefined,
            `cc:location:${id}`
        );
        return response.data.location;
    }

    /**
     * Get location by name
     * @param name - Location name
     * @returns Promise with location data or null
     */
    async getLocationByName(name: string): Promise<GetCCLocationType | null> {
        const locations = await this.getAllLocations();
        return locations.find(location => location.name === name) || null;
    }

    /**
     * Get location by short name
     * @param shortName - Location short name
     * @returns Promise with location data or null
     */
    async getLocationByShortName(shortName: string): Promise<GetCCLocationType | null> {
        const locations = await this.getAllLocations();
        return locations.find(location => location.shortName === shortName) || null;
    }

    /**
     * Get locations by city
     * @param city - City name
     * @returns Promise with locations array
     */
    async getLocationsByCity(city: string): Promise<GetCCLocationType[]> {
        const locations = await this.getAllLocations();
        return locations.filter(location => location.city === city);
    }
}

// Export singleton instance
export const ccLocationRequest = new CCLocationRequest(); 