import { ccApiClient } from './request';
import type { GetCCResourceType } from '@libCC/CCTypes';

/**
 * CC Resource Request methods
 * 
 * This module contains all resource-specific API request methods for the CC platform.
 * It provides a clean interface for making resource-related API calls with proper
 * caching, error handling, and data transformation.
 */
export class CCResourceRequest {
    /**
     * Get all resources
     * @returns Promise with resources array
     */
    async getAllResources(): Promise<GetCCResourceType[]> {
        const response = await ccApiClient.get<{ resources: GetCCResourceType[] }>(
            '/resources',
            undefined,
            'cc:resources'
        );
        return response.data.resources;
    }

    /**
     * Get a resource by ID
     * @param id - Resource ID
     * @returns Promise with resource data
     */
    async getResource(id: number): Promise<GetCCResourceType> {
        const response = await ccApiClient.get<{ resource: GetCCResourceType }>(
            `/resources/${id}`,
            undefined,
            `cc:resource:${id}`
        );
        return response.data.resource;
    }

    /**
     * Get resource by name
     * @param name - Resource name
     * @returns Promise with resource data or null
     */
    async getResourceByName(name: string): Promise<GetCCResourceType | null> {
        const resources = await this.getAllResources();
        return resources.find(resource => resource.name === name) || null;
    }

    /**
     * Get resources by location
     * @param locationId - Location ID
     * @returns Promise with resources array
     */
    async getResourcesByLocation(locationId: number): Promise<GetCCResourceType[]> {
        const resources = await this.getAllResources();
        return resources.filter(resource => resource.location === locationId);
    }

    /**
     * Get resources by IDs
     * @param ids - Array of resource IDs
     * @returns Promise with resources array
     */
    async getResourcesByIds(ids: number[]): Promise<GetCCResourceType[]> {
        const resources = await this.getAllResources();
        return resources.filter(resource => ids.includes(resource.id));
    }
}

// Export singleton instance
export const ccResourceRequest = new CCResourceRequest(); 