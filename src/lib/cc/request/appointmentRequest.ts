import { ccApiClient } from './request';
import { cleanData } from '@utils';
import type { GetCCAppointmentType, PostCCAppointmentType, PutCCAppointmentType, GetCCAppointmentCategory } from '@libCC/CCTypes';

/**
 * CC Appointment Request methods
 * 
 * This module contains all appointment-specific API request methods for the CC platform.
 * It provides a clean interface for making appointment-related API calls with proper
 * caching, error handling, and data transformation.
 */
export class CCAppointmentRequest {
    /**
     * Get an appointment by ID
     * @param id - Appointment ID
     * @returns Promise with appointment data
     */
    async getAppointment(id: number): Promise<GetCCAppointmentType> {
        const response = await ccApiClient.get<{ appointment: GetCCAppointmentType }>(
            `/appointments/${id}`,
            undefined,
            `cc:appointment:${id}`
        );
        return response.data.appointment;
    }

    /**
     * Create a new appointment
     * @param payload - Appointment data to create
     * @returns Promise with created appointment
     */
    async createAppointment(payload: PostCCAppointmentType): Promise<GetCCAppointmentType> {
        const response = await ccApiClient.post<{ appointment: GetCCAppointmentType }>(
            '/appointments',
            { appointment: cleanData(payload) }
        );
        return response.data.appointment;
    }

    /**
     * Update an existing appointment
     * @param id - Appointment ID
     * @param payload - Appointment data to update
     * @returns Promise with updated appointment
     */
    async updateAppointment(id: number, payload: PutCCAppointmentType): Promise<GetCCAppointmentType> {
        const response = await ccApiClient.put<{ appointment: GetCCAppointmentType }>(
            `/appointments/${id}`,
            { appointment: cleanData(payload) }
        );
        return response.data.appointment;
    }

    /**
     * Delete an appointment
     * @param id - Appointment ID
     * @returns Promise with deletion result
     */
    async deleteAppointment(id: number): Promise<void> {
        await ccApiClient.delete(`/appointments/${id}`);
    }

    /**
     * Get appointment category by ID
     * @param id - Category ID
     * @returns Promise with category data
     */
    async getAppointmentCategory(id: number): Promise<GetCCAppointmentCategory> {
        const response = await ccApiClient.get<{ appointmentCategory: GetCCAppointmentCategory }>(
            `/appointmentCategories/${id}`,
            undefined,
            `cc:appointmentCategory:${id}`
        );
        return response.data.appointmentCategory;
    }

    /**
     * Get all appointment categories
     * @returns Promise with categories array
     */
    async getAllAppointmentCategories(): Promise<GetCCAppointmentCategory[]> {
        const response = await ccApiClient.get<{ appointmentCategories: GetCCAppointmentCategory[] }>(
            '/appointmentCategories',
            undefined,
            'cc:appointmentCategories'
        );
        return response.data.appointmentCategories;
    }

    /**
     * Search for appointments
     * @param params - Search parameters
     * @returns Promise with appointments array
     */
    async searchAppointments(params: {
        patient?: number;
        startsAt?: string;
        endsAt?: string;
        page?: number;
        perPage?: number;
        sort?: string;
    } = {}): Promise<GetCCAppointmentType[]> {
        const queryParams = {
            patient: params.patient,
            startsAt: params.startsAt,
            endsAt: params.endsAt,
            'page[number]': params.page || 1,
            'page[size]': params.perPage || 20,
            sort: params.sort || '-startsAt'
        };

        const response = await ccApiClient.get<{ appointments: GetCCAppointmentType[] }>(
            '/appointments',
            cleanData(queryParams),
            `cc:appointments:search:${JSON.stringify(queryParams)}`
        );
        return response.data.appointments;
    }

    /**
     * Get appointments for a specific patient
     * @param patientId - Patient ID
     * @param page - Page number
     * @param perPage - Items per page
     * @returns Promise with appointments array
     */
    async getAppointmentsByPatient(patientId: number, page: number = 1, perPage: number = 20): Promise<GetCCAppointmentType[]> {
        return this.searchAppointments({ patient: patientId, page, perPage });
    }

    /**
     * Get appointments within a date range
     * @param startsAt - Start date
     * @param endsAt - End date
     * @param page - Page number
     * @param perPage - Items per page
     * @returns Promise with appointments array
     */
    async getAppointmentsByDateRange(startsAt: string, endsAt: string, page: number = 1, perPage: number = 20): Promise<GetCCAppointmentType[]> {
        return this.searchAppointments({ startsAt, endsAt, page, perPage });
    }
}

// Export singleton instance
export const ccAppointmentRequest = new CCAppointmentRequest(); 