import { RequestManager, RequestOptions, ApiResponse } from '@lib/base/RequestManager';
import { getConfigs } from '@utils';

/**
 * CC API Client with optimized request handling
 */
export class CCApiClient {
    private readonly requestManager: RequestManager;
    private readonly config = getConfigs();

    constructor() {
        this.requestManager = new RequestManager(
            this.config.ccApiDomain,
            this.config.ccApiKey
        );
    }

    /**
     * Make a GET request to CC API
     * @param path - API endpoint path
     * @param params - Query parameters
     * @param cacheKey - Optional cache key
     * @returns Promise with API response
     */
    async get<T = any>(path: string, params?: Record<string, any>, cacheKey?: string): Promise<ApiResponse<T>> {
        return this.requestManager.request<T>({
            method: 'GET',
            path,
            params,
            cacheKey: cacheKey || `cc:get:${path}:${JSON.stringify(params || {})}`
        });
    }

    /**
     * Make a POST request to CC API
     * @param path - API endpoint path
     * @param data - Request body data
     * @param params - Query parameters
     * @returns Promise with API response
     */
    async post<T = any>(path: string, data?: any, params?: Record<string, any>): Promise<ApiResponse<T>> {
        return this.requestManager.request<T>({
            method: 'POST',
            path,
            data,
            params
        });
    }

    /**
     * Make a PUT request to CC API
     * @param path - API endpoint path
     * @param data - Request body data
     * @param params - Query parameters
     * @returns Promise with API response
     */
    async put<T = any>(path: string, data?: any, params?: Record<string, any>): Promise<ApiResponse<T>> {
        return this.requestManager.request<T>({
            method: 'PUT',
            path,
            data,
            params
        });
    }

    /**
     * Make a DELETE request to CC API
     * @param path - API endpoint path
     * @param params - Query parameters
     * @returns Promise with API response
     */
    async delete<T = any>(path: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
        return this.requestManager.request<T>({
            method: 'DELETE',
            path,
            params
        });
    }

    /**
     * Make a PATCH request to CC API
     * @param path - API endpoint path
     * @param data - Request body data
     * @param params - Query parameters
     * @returns Promise with API response
     */
    async patch<T = any>(path: string, data?: any, params?: Record<string, any>): Promise<ApiResponse<T>> {
        return this.requestManager.request<T>({
            method: 'PATCH',
            path,
            data,
            params
        });
    }

    /**
     * Clear CC API cache
     */
    clearCache(): void {
        this.requestManager.clearCache();
    }

    /**
     * Get CC API cache statistics
     * @returns Cache statistics
     */
    getCacheStats(): { size: number; keys: string[] } {
        return this.requestManager.getCacheStats();
    }
}

// Export singleton instance
export const ccApiClient = new CCApiClient(); 