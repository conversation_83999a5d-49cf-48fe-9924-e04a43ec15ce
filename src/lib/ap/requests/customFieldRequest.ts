import { apApiClient } from './baseRequest';
import { cleanData } from '@utils';
import type { APGetCustomFieldType, APPostCustomfieldType } from '@libAP/APTypes';

/**
 * AP Custom Field Request methods
 * 
 * This module contains all custom field-specific API request methods for the AP platform.
 * It provides a clean interface for making custom field-related API calls with proper
 * caching, error handling, and data transformation.
 */
export class APCustomFieldRequest {
    /**
     * Get a custom field by ID
     * @param id - Custom field ID
     * @param locationId - Location ID
     * @returns Promise with custom field data
     */
    async get(id: string, locationId: string): Promise<APGetCustomFieldType> {
        const response = await apApiClient.get<{ customField: APGetCustomFieldType }>(
            `/locations/${locationId}/customFields/${id}/`,
            undefined,
            `ap:customField:${locationId}:${id}`
        );
        return response.data.customField;
    }

    /**
     * Get all custom fields for a location
     * @param locationId - Location ID
     * @returns Promise with custom fields array
     */
    async all(locationId: string): Promise<APGetCustomFieldType[]> {
        const response = await apApiClient.get<{ customFields: APGetCustomFieldType[] }>(
            `/locations/${locationId}/customFields/`,
            undefined,
            `ap:customFields:${locationId}`
        );
        return response.data.customFields;
    }

    /**
     * Create a new custom field
     * @param data - Custom field data to create
     * @param locationId - Location ID
     * @returns Promise with created custom field
     */
    async create(data: APPostCustomfieldType, locationId: string): Promise<APGetCustomFieldType> {
        const response = await apApiClient.post<{ customField: APGetCustomFieldType }>(
            `/locations/${locationId}/customFields/`,
            cleanData(data)
        );
        return response.data.customField;
    }

    /**
     * Update an existing custom field
     * @param id - Custom field ID
     * @param data - Custom field data to update
     * @param locationId - Location ID
     * @returns Promise with updated custom field
     */
    async update(id: string, data: APPostCustomfieldType, locationId: string): Promise<APGetCustomFieldType> {
        const response = await apApiClient.put<{ customField: APGetCustomFieldType }>(
            `/locations/${locationId}/customFields/${id}/`,
            cleanData(data)
        );
        return response.data.customField;
    }

    /**
     * Delete a custom field
     * @param id - Custom field ID
     * @param locationId - Location ID
     * @returns Promise with deletion result
     */
    async delete(id: string, locationId: string): Promise<boolean> {
        const response = await apApiClient.delete<{ succeeded: boolean }>(
            `/locations/${locationId}/customFields/${id}/`
        );
        return response.data.succeeded;
    }

    /**
     * Get custom field by name
     * @param name - Custom field name
     * @param locationId - Location ID
     * @returns Promise with custom field data or null
     */
    async getCustomFieldByName(name: string, locationId: string): Promise<APGetCustomFieldType | null> {
        const customFields = await this.all(locationId);
        return customFields.find((field: APGetCustomFieldType) => field.name === name) || null;
    }

    /**
     * Get custom field ID by name
     * @param name - Custom field name
     * @param locationId - Location ID
     * @returns Promise with custom field ID or null
     */
    async getCustomFieldIdByName(name: string, locationId: string): Promise<string | null> {
        const customField = await this.getCustomFieldByName(name, locationId);
        return customField?.id || null;
    }

    /**
     * Create custom field if it doesn't exist
     * @param name - Custom field name
     * @param dataType - Custom field data type
     * @param locationId - Location ID
     * @returns Promise with custom field data
     */
    async createCustomFieldIfNotExists(name: string, dataType: string = 'TEXT', locationId: string): Promise<APGetCustomFieldType> {
        const existingField = await this.getCustomFieldByName(name, locationId);
        if (existingField) {
            return existingField;
        }

        return this.create({
            name,
            dataType
        }, locationId);
    }
}

// Export singleton instance
export const apCustomFieldRequest = new APCustomFieldRequest(); 