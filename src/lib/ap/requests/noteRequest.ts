import { apApiClient } from './baseRequest';
import type { GetAPNoteType } from '@libAP/APTypes';

/**
 * AP Note Request methods
 * 
 * This module contains all note-specific API request methods for the AP platform.
 * It provides a clean interface for making note-related API calls with proper
 * caching, error handling, and data transformation.
 */
export class APNoteRequest {
    /**
     * Create a new note for a contact
     * @param contactId - Contact ID
     * @param body - Note content
     * @returns Promise with created note
     */
    async post(contactId: string, body: string): Promise<GetAPNoteType> {
        const response = await apApiClient.post<{ note: GetAPNoteType }>(
            `/contacts/${contactId}/notes/`,
            { body }
        );
        return response.data.note;
    }

    /**
     * Update an existing note
     * @param contactId - Contact ID
     * @param noteId - Note ID
     * @param body - Updated note content
     * @returns Promise with updated note
     */
    async put(contactId: string, noteId: string, body: string): Promise<GetAPNoteType> {
        const response = await apApiClient.put<{ note: GetAPNoteType }>(
            `/contacts/${contactId}/notes/${noteId}/`,
            { body }
        );
        return response.data.note;
    }

    /**
     * Delete a note
     * @param contactId - Contact ID
     * @param noteId - Note ID
     * @returns Promise with deletion result
     */
    async delete(contactId: string, noteId: string): Promise<boolean> {
        const response = await apApiClient.delete<{ succeeded: boolean }>(
            `/contacts/${contactId}/notes/${noteId}/`
        );
        return response.data.succeeded;
    }

    /**
     * Get all notes for a contact
     * @param contactId - Contact ID
     * @returns Promise with notes array
     */
    async getContactNotes(contactId: string): Promise<GetAPNoteType[]> {
        const response = await apApiClient.get<{ notes: GetAPNoteType[] }>(
            `/contacts/${contactId}/notes/`,
            undefined,
            `ap:contact:${contactId}:notes`
        );
        return response.data.notes;
    }

    /**
     * Get a specific note
     * @param contactId - Contact ID
     * @param noteId - Note ID
     * @returns Promise with note data
     */
    async getNote(contactId: string, noteId: string): Promise<GetAPNoteType> {
        const response = await apApiClient.get<{ note: GetAPNoteType }>(
            `/contacts/${contactId}/notes/${noteId}/`,
            undefined,
            `ap:note:${contactId}:${noteId}`
        );
        return response.data.note;
    }

    /**
     * Add appointment note to contact
     * @param contactId - Contact ID
     * @param appointmentTitle - Appointment title
     * @returns Promise with created note
     */
    async addAppointmentNote(contactId: string, appointmentTitle: string): Promise<GetAPNoteType> {
        const noteBody = `Appointment Booked: ${appointmentTitle}`;
        return this.post(contactId, noteBody);
    }

    /**
     * Add cancellation note to contact
     * @param contactId - Contact ID
     * @param appointmentTitle - Appointment title
     * @returns Promise with created note
     */
    async addCancellationNote(contactId: string, appointmentTitle: string): Promise<GetAPNoteType> {
        const noteBody = `Appointment Cancelled: ${appointmentTitle}`;
        return this.post(contactId, noteBody);
    }
}

// Export singleton instance
export const apNoteRequest = new APNoteRequest(); 