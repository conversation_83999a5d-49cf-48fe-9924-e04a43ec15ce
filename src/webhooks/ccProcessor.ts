import type { Context } from "hono";
import type { CCWebhookEvent, WebhookResponse } from "@/types/webhooks";
import { logger, createError, ErrorLogger } from "@utils";
import { ccEventRouter } from "./ccEventRouter";

export const ccWebhookProcessor = async (c: Context): Promise<Response> => {
  const requestId = crypto.randomUUID();

  try {
    const body = (await c.req.json()) as CCWebhookEvent;

    logger.info("CliniCore webhook received", {
      requestId,
      event: body.event,
      model: body.model,
      id: body.id,
      payloadKeys: Object.keys(body.payload || {}),
    });

    // Validate required fields
    if (!body.event || !body.model || !body.payload || !body.id) {
      throw createError(
        "Missing required fields: event, model, payload, or id",
        400
      );
    }

    // Validate event type
    const validEvents = [
      "EntityWasCreated",
      "EntityWasUpdated",
      "EntityWasDeleted",
      "AppointmentWasCreated",
    ];
    if (!validEvents.includes(body.event)) {
      throw createError(`Invalid event type: ${body.event}`, 400);
    }

    // Validate model type
    const validModels = ["Patient", "Appointment", "Invoice", "Payment"];
    if (!validModels.includes(body.model)) {
      throw createError(`Invalid model type: ${body.model}`, 400);
    }

    // Process the event directly (no job queue)
    const result = await ccEventRouter.processEvent(body, requestId);

    const response: WebhookResponse = {
      success: result.success,
      message: result.message,
      requestId,
      processedAt: new Date().toISOString(),
      data: result.data,
    };

    logger.info("CliniCore webhook processed successfully", {
      requestId,
      event: body.event,
      model: body.model,
      id: body.id,
      processingTime: result.processingTime,
    });

    return c.json(response, result.success ? 200 : 500);
  } catch (error) {
    logger.error("CliniCore webhook processing failed", {
      requestId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    // Log to database for tracking
    await ErrorLogger.logError(
      "CCWebhookProcessing",
      error,
      { requestId },
      "ccWebhookProcessor"
    );

    const response: WebhookResponse = {
      success: false,
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
      requestId,
      processedAt: new Date().toISOString(),
    };

    const statusCode =
      error instanceof Error && "status" in error
        ? (error as { status: number }).status
        : 500;
    return c.json(response, statusCode);
  }
};
