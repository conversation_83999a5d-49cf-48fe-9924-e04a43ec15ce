import type { APWebhookEvent } from "@/types/webhooks";
import { logger } from "@utils";

export interface EventProcessingResult {
  success: boolean;
  message: string;
  data?: Record<string, unknown>;
  processingTime?: number;
}

export class APEventRouter {
  async processEvent(
    event: APWebhookEvent,
    requestId: string
  ): Promise<EventProcessingResult> {
    const startTime = Date.now();
    
    try {
      logger.info("Processing AutoPatient webhook event", {
        requestId,
        appointmentId: event.calendar.appointmentId,
        contactId: event.contact_id,
        status: event.calendar.status,
      });

      // TODO: Implement skip logic check using database timestamps
      // const shouldSkip = await skipLogicService.shouldSkipAPEvent(event, requestId)
      // if (shouldSkip.skip) {
      //   return {
      //     success: true,
      //     message: shouldSkip.reason,
      //     processingTime: Date.now() - startTime
      //   }
      // }

      // TODO: Route to appropriate handler
      const result = await this.routeToHandler(event, requestId);
      
      const processingTime = Date.now() - startTime;
      logger.info("AutoPatient event processed successfully", {
        requestId,
        processingTime,
        appointmentId: event.calendar.appointmentId,
      });

      return {
        ...result,
        processingTime,
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      logger.error("AutoPatient event processing failed", {
        requestId,
        appointmentId: event.calendar.appointmentId,
        contactId: event.contact_id,
        processingTime,
        error: error instanceof Error ? error.message : String(error),
      });

      throw error;
    }
  }

  private async routeToHandler(
    event: APWebhookEvent,
    requestId: string
  ): Promise<{ success: boolean; message: string; data?: Record<string, unknown> }> {
    // Placeholder implementation - will be replaced in Task 5
    logger.info("Routing AutoPatient event to handler", {
      requestId,
      appointmentId: event.calendar.appointmentId,
      contactId: event.contact_id,
    });

    // TODO: Implement actual routing logic in Task 5
    return {
      success: true,
      message: "AutoPatient appointment event queued for processing",
      data: {
        appointmentId: event.calendar.appointmentId,
        contactId: event.contact_id,
        status: event.calendar.status,
      },
    };
  }
}

// Export singleton instance
export const apEventRouter = new APEventRouter();
