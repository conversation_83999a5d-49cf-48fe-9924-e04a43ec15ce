import type { Context } from "hono";
import type { APWebhookEvent, WebhookResponse } from "@/types/webhooks";
import { logger, createError, ErrorLogger } from "@utils";
import { apEventRouter } from "./apEventRouter";

export const apWebhookProcessor = async (c: Context): Promise<Response> => {
  const requestId = crypto.randomUUID();

  try {
    const body = (await c.req.json()) as APWebhookEvent;

    logger.info("AutoPatient webhook received", {
      requestId,
      appointmentId: body.calendar?.appointmentId,
      contactId: body.contact_id,
      status: body.calendar?.status,
      source: body.calendar?.created_by_meta?.source,
    });

    // Validate required fields
    if (!body.calendar || !body.contact_id) {
      throw createError("Missing required fields: calendar or contact_id", 400);
    }

    // Validate calendar structure
    if (
      !body.calendar.appointmentId ||
      !body.calendar.startTime ||
      !body.calendar.endTime
    ) {
      throw createError(
        "Missing required calendar fields: appointmentId, startTime, or endTime",
        400
      );
    }

    // Check if this is a CC-originated event to prevent loops
    if (body.calendar.created_by_meta?.source === "third_party") {
      logger.info("AutoPatient webhook skipped - originated from CliniCore", {
        requestId,
        appointmentId: body.calendar.appointmentId,
        source: body.calendar.created_by_meta.source,
      });

      const response: WebhookResponse = {
        success: true,
        message: "Webhook skipped - originated from CliniCore sync",
        requestId,
        processedAt: new Date().toISOString(),
        data: {
          skipped: true,
          reason: "cc_originated",
          appointmentId: body.calendar.appointmentId,
        },
      };

      return c.json(response, 200);
    }

    // Process the event directly
    const result = await apEventRouter.processEvent(body, requestId);

    const response: WebhookResponse = {
      success: result.success,
      message: result.message,
      requestId,
      processedAt: new Date().toISOString(),
      data: result.data,
    };

    logger.info("AutoPatient webhook processed successfully", {
      requestId,
      appointmentId: body.calendar.appointmentId,
      contactId: body.contact_id,
      processingTime: result.processingTime,
    });

    return c.json(response, result.success ? 200 : 500);
  } catch (error) {
    logger.error("AutoPatient webhook processing failed", {
      requestId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    // Log to database for tracking
    await ErrorLogger.logError(
      "APWebhookProcessing",
      error,
      { requestId },
      "apWebhookProcessor"
    );

    const response: WebhookResponse = {
      success: false,
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
      requestId,
      processedAt: new Date().toISOString(),
    };

    const statusCode =
      error instanceof Error && "status" in error
        ? (error as { status: number }).status
        : 500;
    return c.json(response, statusCode);
  }
};
