// CliniCore webhook event structure (same as legacy Socket.io events)
export interface CCWebhookEvent {
  event:
    | "EntityWasCreated"
    | "EntityWasUpdated"
    | "EntityWasDeleted"
    | "AppointmentWasCreated";
  model: "Patient" | "Appointment" | "Invoice" | "Payment";
  id: number;
  payload: CCPatientPayload | CCAppointmentPayload | CCInvoicePayload | CCPaymentPayload;
}

// Exact payload types based on legacy Socket.io data
export interface CCPatientPayload {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  firstName: string;
  lastName: string;
  dob: string | null;
  ssn: string | null;
  flashMessage: string;
  active: boolean;
  phoneMobile: string;
  email: string;
  title: string | null;
  titleSuffix: string | null;
  healthInsurance: string | null;
  gender: string | null;
  addresses: any[];
  categories: number[];
  customFields: number[];
  invoices: number[];
  payments: number[];
  files: number[];
  history: number[];
  appointments: number[];
  messages: any[];
  medications: any[];
  qrUrl: string;
  avatarUrl: string | null;
}

export interface CCAppointmentPayload {
  id: number;
  uuid: string | null;
  startsAt: string;
  endsAt: string;
  arrivedAt: string | null;
  processedAt: string | null;
  treatmentStartedAt: string | null;
  allDay: boolean;
  slot: boolean;
  subject: string | null;
  title: string;
  firstOfPatient: boolean;
  onPatientBirthday: boolean;
  description: string;
  color: string | null;
  patientCategories: number[];
  patientsPreview: any[];
  patients: number[];
  people: number[];
  resources: number[];
  categories: number[];
  location?: number;
  services: number[];
  series: number;
  canceledWhy: string | null;
  createdAt: string;
  updatedAt: string;
  canceledAt: string | null;
  createdBy: number;
  updatedBy: number;
  canceledBy: number | null;
  reminderAt: string | null;
  reminderStatus: string;
  reminderSentAt: string | null;
  deletedAt: string | null;
}

export interface CCInvoicePayload {
  id: number;
  invoiceNumber?: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
  addressText?: null;
  discount?: number;
  status?: string;
  vatId?: null;
  description?: string;
  note?: null;
  canceledWhy?: null;
  settlementStatus?: null;
  pdfUrl?: string;
  invoiceNumberSequence?: string;
  address?: {
    id?: number;
    label?: null;
    name?: string;
    street?: null;
    streetNumber?: null;
    postalCode?: null;
    city?: null;
    country?: string;
    primary?: number;
  };
  appointment?: null;
  positions?: {
    id?: number;
    name?: string;
    gross?: number;
    discount?: number;
    [key: string]: any;
  }[];
  reversedBy?: null;
  reverses?: null;
  patient?: number;
  payments?: {
    id?: number;
    [key: string]: any;
  }[];
  practicioner?: number;
  settlement?: null;
  sentAt?: null;
  wahonlinedAt?: null;
  diagnoses?: {
    id?: number;
    [key: string]: any;
  }[];
}

export interface CCPaymentPayload {
  id: number;
  paymentNumber: string;
  gross: number;
  customIdentification: null;
  comment: string;
  createdAt: string;
  date: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  register: number;
  patient: number;
  invoicePayments: {
    id: number;
    gross: number;
    invoice: number;
    payment: number;
  }[];
  reversedBy: null;
  reverses: null;
  pdfUrl: string;
  canceled: boolean;
}

// AutoPatient webhook structure (for bidirectional sync)
export interface APWebhookEvent {
  calendar: {
    id: string;
    appointmentId: string;
    startTime: string;
    endTime: string;
    status: string;
    created_by_meta: {
      source: string;
      channel?: string;
    };
    last_updated_by_meta?: {
      source: string;
      channel?: string;
    };
  };
  contact_id: string;
  email?: string;
  phone?: string;
}

export interface WebhookContext {
  event: CCWebhookEvent | APWebhookEvent;
  requestId: string;
}

export interface WebhookResponse {
  success: boolean;
  message: string;
  requestId: string;
  processedAt: string;
  data?: Record<string, unknown>;
}
