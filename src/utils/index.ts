import { getContext } from 'hono/context-storage';

export { default as cleanData } from './cleanData';
export { default as getConfig, getConfigs } from './configs';
export { default as createError } from './createError';
export { ErrorLogger } from './errorLogger';
export { logger } from './logger';

const getEnv = () => {
    const ctx = getContext<Env>();
    if (!ctx.env) {
        throw new Error('Env not found');
    }
    return ctx.env;
}

const getKV = () => {
    return getEnv().kv;
}