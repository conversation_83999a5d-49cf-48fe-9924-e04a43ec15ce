import { relations } from 'drizzle-orm';
import { pgTable, timestamp, varchar, integer, jsonb, text } from 'drizzle-orm/pg-core';


const commonColumns = {
    id: varchar('id', { length: 255 }).primaryKey().$defaultFn(() => crypto.randomUUID()),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),
}

export const patient = pgTable('patients', {
    ...commonColumns,
    apId: varchar('ap_id', { length: 255 }).notNull().unique(),
    ccId: integer('cc_id').notNull().unique(),
    email: varchar('email', { length: 255 }),
    phone: varchar('phone', { length: 255 }),
    apUpdatedAt: timestamp('ap_updated_at'),
    ccUpdatedAt: timestamp('cc_updated_at'),
    apData: jsonb('ap_data'),
    ccData: jsonb('cc_data'),
});

export const appointment = pgTable('appointments', {
    ...commonColumns,
    apId: varchar('ap_id', { length: 255 }).notNull().unique(),
    ccId: integer('cc_id').notNull().unique(),
    patientId: varchar('patient_id', { length: 255 }).references(() => patient.id),
    apUpdatedAt: timestamp('ap_updated_at'),
    ccUpdatedAt: timestamp('cc_updated_at'),
    apData: jsonb('ap_data'),
    ccData: jsonb('cc_data'),
    apNoteID: text('ap_note_id'),
});

export const apCustomFields = pgTable('ap_custom_fields', {
    ...commonColumns,
    apId: varchar('ap_id', { length: 255 }).unique(),
    name: varchar('name', { length: 255 }).notNull(),
    config: jsonb('config'),
});

export const ccCustomFields = pgTable('cc_custom_fields', {
    ...commonColumns,
    ccId: integer('cc_id').unique(),
    name: varchar('name', { length: 255 }).notNull(),
    config: jsonb('config'),
});

export const errorLogs = pgTable('error_logs', {
    ...commonColumns,
    message: text('message').notNull(),
    stack: text('stack'),
    type: varchar('type', { length: 255 }).notNull(),
    data: jsonb('data'),
});

export const patientAppointmentsRelation = relations(patient, ({ many }) => ({
    appointments: many(appointment),
}));

export const appointmentPatientRelation = relations(appointment, ({ one }) => ({
    patient: one(patient, {
        fields: [appointment.patientId],
        references: [patient.id],
    }),
}));

const dbSchema = { patient, appointment, apCustomFields, ccCustomFields, errorLogs }
export default dbSchema;